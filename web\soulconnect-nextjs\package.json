{"name": "soulconnect-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.2.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "jsonwebtoken": "^9.0.2", "next": "15.3.3", "next-pwa": "^5.6.0", "react": "^19.0.0", "react-dom": "^19.0.0", "socket.io-client": "^4.8.1", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}