<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SoulConnect - Anonymous Friendship Builder</title>
    
    <!-- PWA Meta Tags -->
    <meta name="application-name" content="SoulConnect">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="SoulConnect">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#2563eb">
    
    <!-- Icons -->
    <link rel="manifest" href="manifest.json">
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><circle cx='50' cy='50' r='50' fill='%232563eb'/><text y='70' font-size='60' text-anchor='middle' x='50' fill='white'>💬</text></svg>">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        .install-prompt {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: #2563eb;
            color: white;
            padding: 1rem;
            z-index: 50;
            display: none;
        }
        .install-prompt.show {
            display: block;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-purple-50 min-h-screen">
    <!-- PWA Install Prompt -->
    <div id="installPrompt" class="install-prompt">
        <div class="max-w-4xl mx-auto flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <span class="text-2xl">📱</span>
                <span class="font-medium">Install SoulConnect as an app for the best experience!</span>
            </div>
            <div class="flex space-x-2">
                <button id="installBtn" class="bg-white text-blue-600 px-4 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                    Install
                </button>
                <button id="dismissBtn" class="text-white hover:text-gray-200 px-2">
                    ✕
                </button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="flex-1 flex items-center justify-center px-4 py-12" id="mainContent">
        <div class="max-w-4xl mx-auto text-center">
            <!-- Header -->
            <div class="mb-8">
                <h1 class="text-5xl md:text-6xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
                    SoulConnect
                </h1>
                <p class="text-xl md:text-2xl text-gray-600 mb-2">
                    Anonymous Friendship Builder
                </p>
                <p class="text-lg text-gray-500 max-w-2xl mx-auto">
                    Talk to someone who doesn't judge, doesn't know your name, but understands you.
                </p>
            </div>

            <!-- Features Grid -->
            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6 text-center">
                    <div class="text-4xl mb-4">💬</div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Anonymous Chat</h3>
                    <p class="text-gray-600 text-sm">Connect safely without revealing identity</p>
                </div>
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6 text-center">
                    <div class="text-4xl mb-4">❤️</div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Emotional Support</h3>
                    <p class="text-gray-600 text-sm">Find understanding and empathy</p>
                </div>
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6 text-center">
                    <div class="text-4xl mb-4">🔒</div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Safe & Secure</h3>
                    <p class="text-gray-600 text-sm">Built-in safety features</p>
                </div>
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6 text-center">
                    <div class="text-4xl mb-4">🎯</div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Smart Matching</h3>
                    <p class="text-gray-600 text-sm">Based on mood and interests</p>
                </div>
            </div>

            <!-- Auth Section -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-8 max-w-md mx-auto">
                <h2 class="text-2xl font-bold text-gray-800 mb-4" id="authTitle">
                    Ready to Connect?
                </h2>
                <p class="text-gray-600 mb-6" id="authSubtitle">
                    Create your account to start connecting
                </p>
                
                <div id="errorMessage" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4 hidden"></div>

                <form id="authForm" class="space-y-4">
                    <input
                        type="text"
                        id="username"
                        placeholder="Username"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
                        required
                    />
                    
                    <input
                        type="email"
                        id="email"
                        placeholder="Email"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
                        required
                    />
                    
                    <input
                        type="password"
                        id="password"
                        placeholder="Password"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
                        required
                    />

                    <div class="mb-6" id="ageConfirmContainer">
                        <label class="flex items-center justify-center space-x-3 cursor-pointer">
                            <input
                                type="checkbox"
                                id="ageConfirm"
                                class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            />
                            <span class="text-sm text-gray-700">
                                I confirm that I am 13 years or older
                            </span>
                        </label>
                    </div>

                    <button
                        type="submit"
                        id="submitBtn"
                        class="w-full py-3 px-6 rounded-lg font-semibold text-lg transition-all duration-200 bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl"
                    >
                        Create Account
                    </button>
                </form>
                
                <div class="mt-4 text-center">
                    <button id="switchBtn" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                        Already have an account? Sign in
                    </button>
                </div>

                <!-- Success Message -->
                <div class="bg-green-50 border border-green-200 rounded-xl p-6 mt-6">
                    <div class="text-center">
                        <div class="text-4xl mb-4">🎉</div>
                        <h3 class="text-xl font-bold text-green-800 mb-2">
                            SoulConnect PWA Ready!
                        </h3>
                        <p class="text-green-700 mb-4">
                            ✅ Real backend with authentication<br>
                            ✅ PWA installable as mobile app<br>
                            ✅ Works offline<br>
                            ✅ Push notifications ready
                        </p>
                        <div class="grid md:grid-cols-2 gap-4 text-left">
                            <div class="space-y-2">
                                <h4 class="font-semibold text-green-800">📱 Mobile Access:</h4>
                                <ul class="text-sm text-green-700 space-y-1">
                                    <li>• Visit: <code class="bg-green-100 px-1 rounded">192.168.79.8:3000</code></li>
                                    <li>• Tap "Install" when prompted</li>
                                    <li>• Add to home screen</li>
                                </ul>
                            </div>
                            <div class="space-y-2">
                                <h4 class="font-semibold text-green-800">🚀 Features:</h4>
                                <ul class="text-sm text-green-700 space-y-1">
                                    <li>• Real user accounts</li>
                                    <li>• Secure authentication</li>
                                    <li>• Data persistence</li>
                                    <li>• Mobile optimized</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white border-t border-gray-200 py-6">
        <div class="max-w-4xl mx-auto px-4 text-center">
            <p class="text-gray-500 text-sm">
                © 2024 SoulConnect. Built with ❤️ for mental health and human connection.
            </p>
        </div>
    </footer>

    <script>
        // PWA Installation
        let deferredPrompt;
        const installPrompt = document.getElementById('installPrompt');
        const installBtn = document.getElementById('installBtn');
        const dismissBtn = document.getElementById('dismissBtn');

        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            installPrompt.classList.add('show');
            document.getElementById('mainContent').style.paddingTop = '5rem';
        });

        installBtn.addEventListener('click', async () => {
            if (!deferredPrompt) return;
            deferredPrompt.prompt();
            const { outcome } = await deferredPrompt.userChoice;
            if (outcome === 'accepted') {
                installPrompt.classList.remove('show');
                document.getElementById('mainContent').style.paddingTop = '3rem';
            }
            deferredPrompt = null;
        });

        dismissBtn.addEventListener('click', () => {
            installPrompt.classList.remove('show');
            document.getElementById('mainContent').style.paddingTop = '3rem';
        });

        // Auth Form Logic
        let showLogin = false;
        const authForm = document.getElementById('authForm');
        const authTitle = document.getElementById('authTitle');
        const authSubtitle = document.getElementById('authSubtitle');
        const emailField = document.getElementById('email');
        const ageConfirmContainer = document.getElementById('ageConfirmContainer');
        const submitBtn = document.getElementById('submitBtn');
        const switchBtn = document.getElementById('switchBtn');
        const errorMessage = document.getElementById('errorMessage');

        function toggleAuthMode() {
            showLogin = !showLogin;
            if (showLogin) {
                authTitle.textContent = 'Welcome Back!';
                authSubtitle.textContent = 'Sign in to continue your journey';
                emailField.style.display = 'none';
                ageConfirmContainer.style.display = 'none';
                submitBtn.textContent = 'Sign In';
                switchBtn.textContent = "Don't have an account? Sign up";
            } else {
                authTitle.textContent = 'Ready to Connect?';
                authSubtitle.textContent = 'Create your account to start connecting';
                emailField.style.display = 'block';
                ageConfirmContainer.style.display = 'block';
                submitBtn.textContent = 'Create Account';
                switchBtn.textContent = 'Already have an account? Sign in';
            }
        }

        switchBtn.addEventListener('click', toggleAuthMode);

        authForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const ageConfirm = document.getElementById('ageConfirm').checked;

            if (!showLogin && !ageConfirm) {
                showError('Please confirm you are 13 or older to continue.');
                return;
            }

            submitBtn.textContent = 'Please wait...';
            submitBtn.disabled = true;

            try {
                const endpoint = showLogin ? '/api/auth/login' : '/api/auth/register';
                const body = showLogin ? { username, password } : { username, email, password };
                
                const response = await fetch(`http://192.168.29.8:8080/api${endpoint}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(body),
                });

                const data = await response.json();

                if (data.success) {
                    localStorage.setItem('soulconnect_token', data.token);
                    localStorage.setItem('soulconnect_user', JSON.stringify(data.user));
                    showSuccess(`${showLogin ? 'Login' : 'Registration'} successful! Welcome to SoulConnect!`);
                    // Redirect to dashboard after 2 seconds
                    setTimeout(() => {
                        window.location.href = '/dashboard';
                    }, 2000);
                } else {
                    showError(data.error || 'Something went wrong');
                }
            } catch (error) {
                showError('Network error. Make sure your backend server is running.');
            } finally {
                submitBtn.textContent = showLogin ? 'Sign In' : 'Create Account';
                submitBtn.disabled = false;
            }
        });

        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.classList.remove('hidden');
            setTimeout(() => {
                errorMessage.classList.add('hidden');
            }, 5000);
        }

        function showSuccess(message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-4';
            successDiv.textContent = message;
            errorMessage.parentNode.insertBefore(successDiv, errorMessage);
            setTimeout(() => {
                successDiv.remove();
            }, 5000);
        }

        // Service Worker Registration
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then((registration) => {
                        console.log('SW registered: ', registration);
                    })
                    .catch((registrationError) => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    </script>
</body>
</html>
