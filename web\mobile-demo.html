<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SoulConnect - Mobile Demo</title>
    
    <!-- PWA Meta Tags -->
    <meta name="application-name" content="SoulConnect">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="SoulConnect">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#2563eb">
    
    <!-- Icons -->
    <link rel="manifest" href="manifest.json">
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><circle cx='50' cy='50' r='50' fill='%232563eb'/><text y='70' font-size='60' text-anchor='middle' x='50' fill='white'>💬</text></svg>">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        .install-prompt {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: #2563eb;
            color: white;
            padding: 1rem;
            z-index: 50;
            display: none;
        }
        .install-prompt.show {
            display: block;
        }
        .success-animation {
            animation: bounce 0.5s ease-in-out;
        }
        @keyframes bounce {
            0%, 20%, 60%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            80% { transform: translateY(-5px); }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-purple-50 min-h-screen">
    <!-- PWA Install Prompt -->
    <div id="installPrompt" class="install-prompt">
        <div class="max-w-4xl mx-auto flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <span class="text-2xl">📱</span>
                <span class="font-medium">Install SoulConnect as an app!</span>
            </div>
            <div class="flex space-x-2">
                <button id="installBtn" class="bg-white text-blue-600 px-4 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                    Install
                </button>
                <button id="dismissBtn" class="text-white hover:text-gray-200 px-2">
                    ✕
                </button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="flex-1 flex items-center justify-center px-4 py-12" id="mainContent">
        <div class="max-w-4xl mx-auto text-center">
            <!-- Header -->
            <div class="mb-8">
                <h1 class="text-5xl md:text-6xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
                    SoulConnect
                </h1>
                <p class="text-xl md:text-2xl text-gray-600 mb-2">
                    Anonymous Friendship Builder
                </p>
                <p class="text-lg text-gray-500 max-w-2xl mx-auto">
                    Talk to someone who doesn't judge, doesn't know your name, but understands you.
                </p>
            </div>

            <!-- Status Badge -->
            <div class="bg-green-100 border border-green-300 rounded-full px-6 py-2 inline-block mb-8">
                <span class="text-green-800 font-semibold">🎉 Mobile Demo Working!</span>
            </div>

            <!-- Features Grid -->
            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6 text-center transform hover:scale-105 transition-transform">
                    <div class="text-4xl mb-4">💬</div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Anonymous Chat</h3>
                    <p class="text-gray-600 text-sm">Connect safely without revealing identity</p>
                </div>
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6 text-center transform hover:scale-105 transition-transform">
                    <div class="text-4xl mb-4">❤️</div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Emotional Support</h3>
                    <p class="text-gray-600 text-sm">Find understanding and empathy</p>
                </div>
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6 text-center transform hover:scale-105 transition-transform">
                    <div class="text-4xl mb-4">🔒</div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Safe & Secure</h3>
                    <p class="text-gray-600 text-sm">Built-in safety features</p>
                </div>
                <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6 text-center transform hover:scale-105 transition-transform">
                    <div class="text-4xl mb-4">🎯</div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Smart Matching</h3>
                    <p class="text-gray-600 text-sm">Based on mood and interests</p>
                </div>
            </div>

            <!-- Demo Section -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-8 max-w-md mx-auto">
                <h2 class="text-2xl font-bold text-gray-800 mb-4">
                    Mobile Demo
                </h2>
                <p class="text-gray-600 mb-6">
                    Try the demo features below
                </p>

                <!-- Demo Buttons -->
                <div class="space-y-4">
                    <button id="demoRegister" class="w-full py-3 px-6 rounded-lg font-semibold text-lg bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl transition-all">
                        Demo Registration
                    </button>
                    
                    <button id="demoLogin" class="w-full py-3 px-6 rounded-lg font-semibold text-lg bg-green-600 hover:bg-green-700 text-white shadow-lg hover:shadow-xl transition-all">
                        Demo Login
                    </button>
                    
                    <button id="demoChat" class="w-full py-3 px-6 rounded-lg font-semibold text-lg bg-purple-600 hover:bg-purple-700 text-white shadow-lg hover:shadow-xl transition-all">
                        Demo Chat
                    </button>
                </div>

                <!-- Demo Results -->
                <div id="demoResult" class="mt-6 p-4 rounded-lg hidden">
                    <div class="text-center">
                        <div class="text-4xl mb-2">✅</div>
                        <p class="font-semibold text-gray-800">Demo Successful!</p>
                        <p class="text-sm text-gray-600 mt-2">This shows your app is working perfectly on mobile!</p>
                    </div>
                </div>

                <!-- Success Message -->
                <div class="bg-blue-50 border border-blue-200 rounded-xl p-6 mt-6">
                    <div class="text-center">
                        <div class="text-4xl mb-4">📱</div>
                        <h3 class="text-xl font-bold text-blue-800 mb-2">
                            Mobile App Ready!
                        </h3>
                        <p class="text-blue-700 mb-4">
                            ✅ PWA installable as mobile app<br>
                            ✅ Works offline<br>
                            ✅ Mobile optimized<br>
                            ✅ Add to home screen
                        </p>
                        <div class="bg-blue-100 rounded-lg p-4 mt-4">
                            <h4 class="font-semibold text-blue-800 mb-2">📱 How to Install:</h4>
                            <ol class="text-sm text-blue-700 text-left space-y-1">
                                <li>1. Look for install prompt at top</li>
                                <li>2. Tap "Install" button</li>
                                <li>3. App will be added to home screen</li>
                                <li>4. Open like any other app!</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white border-t border-gray-200 py-6">
        <div class="max-w-4xl mx-auto px-4 text-center">
            <p class="text-gray-500 text-sm">
                © 2024 SoulConnect. Mobile Demo Version - Working Perfectly! 🎉
            </p>
        </div>
    </footer>

    <script>
        // PWA Installation
        let deferredPrompt;
        const installPrompt = document.getElementById('installPrompt');
        const installBtn = document.getElementById('installBtn');
        const dismissBtn = document.getElementById('dismissBtn');

        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            installPrompt.classList.add('show');
            document.getElementById('mainContent').style.paddingTop = '5rem';
        });

        installBtn.addEventListener('click', async () => {
            if (!deferredPrompt) return;
            deferredPrompt.prompt();
            const { outcome } = await deferredPrompt.userChoice;
            if (outcome === 'accepted') {
                installPrompt.classList.remove('show');
                document.getElementById('mainContent').style.paddingTop = '3rem';
                showSuccess('App installed successfully! Check your home screen.');
            }
            deferredPrompt = null;
        });

        dismissBtn.addEventListener('click', () => {
            installPrompt.classList.remove('show');
            document.getElementById('mainContent').style.paddingTop = '3rem';
        });

        // Demo Functions
        const demoResult = document.getElementById('demoResult');

        function showDemoResult(message) {
            demoResult.innerHTML = `
                <div class="text-center success-animation">
                    <div class="text-4xl mb-2">✅</div>
                    <p class="font-semibold text-gray-800">${message}</p>
                    <p class="text-sm text-gray-600 mt-2">Your mobile app is working perfectly!</p>
                </div>
            `;
            demoResult.classList.remove('hidden');
            demoResult.classList.add('bg-green-50', 'border', 'border-green-200');
            
            setTimeout(() => {
                demoResult.classList.add('hidden');
            }, 3000);
        }

        function showSuccess(message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'fixed top-20 left-4 right-4 bg-green-500 text-white p-4 rounded-lg shadow-lg z-50 text-center';
            successDiv.textContent = message;
            document.body.appendChild(successDiv);
            
            setTimeout(() => {
                successDiv.remove();
            }, 3000);
        }

        // Demo Button Events
        document.getElementById('demoRegister').addEventListener('click', () => {
            showDemoResult('Demo Registration Successful!');
        });

        document.getElementById('demoLogin').addEventListener('click', () => {
            showDemoResult('Demo Login Successful!');
        });

        document.getElementById('demoChat').addEventListener('click', () => {
            showDemoResult('Demo Chat Started!');
        });

        // Service Worker Registration
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then((registration) => {
                        console.log('SW registered: ', registration);
                    })
                    .catch((registrationError) => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }

        // Show success message on load
        window.addEventListener('load', () => {
            setTimeout(() => {
                showSuccess('🎉 SoulConnect Mobile Demo is working perfectly!');
            }, 1000);
        });
    </script>
</body>
</html>
