<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Circle -->
  <circle cx="256" cy="256" r="256" fill="url(#gradient)"/>
  
  <!-- Heart Icon -->
  <path d="M256 416c-7.5 0-15-2.5-20.5-7.5L108 281c-39-39-39-102 0-141s102-39 141 0l7 7 7-7c39-39 102-39 141 0s39 102 0 141L276.5 408.5c-5.5 5-13 7.5-20.5 7.5z" fill="white" opacity="0.9"/>
  
  <!-- Chat <PERSON>ubble -->
  <path d="M352 96H160c-35.3 0-64 28.7-64 64v128c0 35.3 28.7 64 64 64h64l32 32 32-32h64c35.3 0 64-28.7 64-64V160c0-35.3-28.7-64-64-64z" fill="white" opacity="0.8"/>
  
  <!-- Chat Dots -->
  <circle cx="208" cy="224" r="16" fill="#2563eb"/>
  <circle cx="256" cy="224" r="16" fill="#2563eb"/>
  <circle cx="304" cy="224" r="16" fill="#2563eb"/>
  
  <!-- Gradient Definition -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
