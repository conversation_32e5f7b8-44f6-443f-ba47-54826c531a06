const express = require('express');
const path = require('path');
const app = express();
const PORT = 8000;

// Serve static files from current directory
app.use(express.static(__dirname));

// Specific route for test.html
app.get('/test.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'test.html'));
});

// Specific route for mobile-app.html
app.get('/mobile-app.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'mobile-app.html'));
});

// Specific route for mobile-demo.html
app.get('/mobile-demo.html', (req, res) => {
    res.sendFile(path.join(__dirname, 'mobile-demo.html'));
});

// Root route
app.get('/', (req, res) => {
    res.send(`
        <h1>SoulConnect Server</h1>
        <p>Available files:</p>
        <ul>
            <li><a href="/test.html">test.html</a></li>
            <li><a href="/mobile-app.html">mobile-app.html</a></li>
            <li><a href="/mobile-demo.html">mobile-demo.html</a></li>
        </ul>
    `);
});

app.listen(PORT, '0.0.0.0', () => {
    console.log(`Server running at http://localhost:${PORT}`);
    console.log(`Network access: http://************:${PORT}`);
});
