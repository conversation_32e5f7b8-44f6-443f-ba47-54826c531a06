(()=>{var __webpack_modules__={"./dist/compiled/@edge-runtime/cookies/index.js":/*!******************************************************!*\
  !*** ./dist/compiled/@edge-runtime/cookies/index.js ***!
  \******************************************************/module1=>{"use strict";var __defProp=Object.defineProperty,__getOwnPropDesc=Object.getOwnPropertyDescriptor,__getOwnPropNames=Object.getOwnPropertyNames,__hasOwnProp=Object.prototype.hasOwnProperty,src_exports={};function stringifyCookie(c){var _a;let attrs=["path"in c&&c.path&&`Path=${c.path}`,"expires"in c&&(c.expires||0===c.expires)&&`Expires=${("number"==typeof c.expires?new Date(c.expires):c.expires).toUTCString()}`,"maxAge"in c&&"number"==typeof c.maxAge&&`Max-Age=${c.maxAge}`,"domain"in c&&c.domain&&`Domain=${c.domain}`,"secure"in c&&c.secure&&"Secure","httpOnly"in c&&c.httpOnly&&"HttpOnly","sameSite"in c&&c.sameSite&&`SameSite=${c.sameSite}`,"partitioned"in c&&c.partitioned&&"Partitioned","priority"in c&&c.priority&&`Priority=${c.priority}`].filter(Boolean),stringified=`${c.name}=${encodeURIComponent(null!=(_a=c.value)?_a:"")}`;return 0===attrs.length?stringified:`${stringified}; ${attrs.join("; ")}`}function parseCookie(cookie){let map=/* @__PURE__ */new Map;for(let pair of cookie.split(/; */)){if(!pair)continue;let splitAt=pair.indexOf("=");if(-1===splitAt){map.set(pair,"true");continue}let[key,value]=[pair.slice(0,splitAt),pair.slice(splitAt+1)];try{map.set(key,decodeURIComponent(null!=value?value:"true"))}catch{}}return map}function parseSetCookie(setCookie){var string,string1;if(!setCookie)return;let[[name,value],...attributes]=parseCookie(setCookie),{domain,expires,httponly,maxage,path,samesite,secure,partitioned,priority}=Object.fromEntries(attributes.map(([key,value2])=>[key.toLowerCase().replace(/-/g,""),value2]));return function(t){let newT={};for(let key in t)t[key]&&(newT[key]=t[key]);return newT}({name,value:decodeURIComponent(value),domain,...expires&&{expires:new Date(expires)},...httponly&&{httpOnly:!0},..."string"==typeof maxage&&{maxAge:Number(maxage)},path,...samesite&&{sameSite:SAME_SITE.includes(string=(string=samesite).toLowerCase())?string:void 0},...secure&&{secure:!0},...priority&&{priority:PRIORITY.includes(string1=(string1=priority).toLowerCase())?string1:void 0},...partitioned&&{partitioned:!0}})}((target,all)=>{for(var name in all)__defProp(target,name,{get:all[name],enumerable:!0})})(src_exports,{RequestCookies:()=>RequestCookies,ResponseCookies:()=>ResponseCookies,parseCookie:()=>parseCookie,parseSetCookie:()=>parseSetCookie,stringifyCookie:()=>stringifyCookie}),module1.exports=((to,from,except,desc)=>{if(from&&"object"==typeof from||"function"==typeof from)for(let key of __getOwnPropNames(from))__hasOwnProp.call(to,key)||void 0===key||__defProp(to,key,{get:()=>from[key],enumerable:!(desc=__getOwnPropDesc(from,key))||desc.enumerable});return to})(__defProp({},"__esModule",{value:!0}),src_exports);var SAME_SITE=["strict","lax","none"],PRIORITY=["low","medium","high"],RequestCookies=class{constructor(requestHeaders){this._parsed=/* @__PURE__ */new Map,this._headers=requestHeaders;let header=requestHeaders.get("cookie");if(header)for(let[name,value]of parseCookie(header))this._parsed.set(name,{name,value})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...args){let name="string"==typeof args[0]?args[0]:args[0].name;return this._parsed.get(name)}getAll(...args){var _a;let all=Array.from(this._parsed);if(!args.length)return all.map(([_,value])=>value);let name="string"==typeof args[0]?args[0]:null==(_a=args[0])?void 0:_a.name;return all.filter(([n])=>n===name).map(([_,value])=>value)}has(name){return this._parsed.has(name)}set(...args){let[name,value]=1===args.length?[args[0].name,args[0].value]:args,map=this._parsed;return map.set(name,{name,value}),this._headers.set("cookie",Array.from(map).map(([_,value2])=>stringifyCookie(value2)).join("; ")),this}delete(names){let map=this._parsed,result=Array.isArray(names)?names.map(name=>map.delete(name)):map.delete(names);return this._headers.set("cookie",Array.from(map).map(([_,value])=>stringifyCookie(value)).join("; ")),result}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(v=>`${v.name}=${encodeURIComponent(v.value)}`).join("; ")}},ResponseCookies=class{constructor(responseHeaders){var _a,_b,_c;this._parsed=/* @__PURE__ */new Map,this._headers=responseHeaders;let setCookie=null!=(_c=null!=(_b=null==(_a=responseHeaders.getSetCookie)?void 0:_a.call(responseHeaders))?_b:responseHeaders.get("set-cookie"))?_c:[];for(let cookieString of Array.isArray(setCookie)?setCookie:function(cookiesString){if(!cookiesString)return[];var start,ch,lastComma,nextStart,cookiesSeparatorFound,cookiesStrings=[],pos=0;function skipWhitespace(){for(;pos<cookiesString.length&&/\s/.test(cookiesString.charAt(pos));)pos+=1;return pos<cookiesString.length}for(;pos<cookiesString.length;){for(start=pos,cookiesSeparatorFound=!1;skipWhitespace();)if(","===(ch=cookiesString.charAt(pos))){for(lastComma=pos,pos+=1,skipWhitespace(),nextStart=pos;pos<cookiesString.length&&"="!==(ch=cookiesString.charAt(pos))&&";"!==ch&&","!==ch;)pos+=1;pos<cookiesString.length&&"="===cookiesString.charAt(pos)?(cookiesSeparatorFound=!0,pos=nextStart,cookiesStrings.push(cookiesString.substring(start,lastComma)),start=pos):pos=lastComma+1}else pos+=1;(!cookiesSeparatorFound||pos>=cookiesString.length)&&cookiesStrings.push(cookiesString.substring(start,cookiesString.length))}return cookiesStrings}(setCookie)){let parsed=parseSetCookie(cookieString);parsed&&this._parsed.set(parsed.name,parsed)}}get(...args){let key="string"==typeof args[0]?args[0]:args[0].name;return this._parsed.get(key)}getAll(...args){var _a;let all=Array.from(this._parsed.values());if(!args.length)return all;let key="string"==typeof args[0]?args[0]:null==(_a=args[0])?void 0:_a.name;return all.filter(c=>c.name===key)}has(name){return this._parsed.has(name)}set(...args){let[name,value,cookie]=1===args.length?[args[0].name,args[0].value,args[0]]:args,map=this._parsed;return map.set(name,function(cookie={name:"",value:""}){return"number"==typeof cookie.expires&&(cookie.expires=new Date(cookie.expires)),cookie.maxAge&&(cookie.expires=new Date(Date.now()+1e3*cookie.maxAge)),(null===cookie.path||void 0===cookie.path)&&(cookie.path="/"),cookie}({name,value,...cookie})),function(bag,headers){for(let[,value]of(headers.delete("set-cookie"),bag)){let serialized=stringifyCookie(value);headers.append("set-cookie",serialized)}}(map,this._headers),this}delete(...args){let[name,options]="string"==typeof args[0]?[args[0]]:[args[0].name,args[0]];return this.set({...options,name,value:"",expires:/* @__PURE__ */new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(stringifyCookie).join("; ")}}},"./dist/compiled/cookie/index.js":/*!***************************************!*\
  !*** ./dist/compiled/cookie/index.js ***!
  \***************************************/module1=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var e={};(()=>{/*!
 * cookie
 * Copyright(c) 2012-2014 Roman Shtylman
 * Copyright(c) 2015 Douglas Christopher Wilson
 * MIT Licensed
 */e.parse=function(e,r){if("string"!=typeof e)throw TypeError("argument str must be a string");for(var t={},o=e.split(a),s=(r||{}).decode||i,p=0;p<o.length;p++){var f=o[p],u=f.indexOf("=");if(!(u<0)){var v=f.substr(0,u).trim(),c=f.substr(++u,f.length).trim();'"'==c[0]&&(c=c.slice(1,-1)),void 0==t[v]&&(t[v]=function(e,r){try{return r(e)}catch(r){return e}}(c,s))}}return t},e.serialize=function(e,r,i){var a=i||{},o=a.encode||t;if("function"!=typeof o)throw TypeError("option encode is invalid");if(!n.test(e))throw TypeError("argument name is invalid");var s=o(r);if(s&&!n.test(s))throw TypeError("argument val is invalid");var p=e+"="+s;if(null!=a.maxAge){var f=a.maxAge-0;if(isNaN(f)||!isFinite(f))throw TypeError("option maxAge is invalid");p+="; Max-Age="+Math.floor(f)}if(a.domain){if(!n.test(a.domain))throw TypeError("option domain is invalid");p+="; Domain="+a.domain}if(a.path){if(!n.test(a.path))throw TypeError("option path is invalid");p+="; Path="+a.path}if(a.expires){if("function"!=typeof a.expires.toUTCString)throw TypeError("option expires is invalid");p+="; Expires="+a.expires.toUTCString()}if(a.httpOnly&&(p+="; HttpOnly"),a.secure&&(p+="; Secure"),a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":p+="; SameSite=Strict";break;case"lax":p+="; SameSite=Lax";break;case"none":p+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return p};var i=decodeURIComponent,t=encodeURIComponent,a=/; */,n=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),module1.exports=e})()},"./dist/compiled/react-is/cjs/react-is.development.js":/*!************************************************************!*\
  !*** ./dist/compiled/react-is/cjs/react-is.development.js ***!
  \************************************************************/(__unused_webpack_module,exports)=>{"use strict";/**
 * @license React
 * react-is.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */!function(){function typeOf(object){if("object"==typeof object&&null!==object){var $$typeof=object.$$typeof;switch($$typeof){case REACT_ELEMENT_TYPE:switch(object=object.type){case REACT_FRAGMENT_TYPE:case REACT_PROFILER_TYPE:case REACT_STRICT_MODE_TYPE:case REACT_SUSPENSE_TYPE:case REACT_SUSPENSE_LIST_TYPE:case REACT_VIEW_TRANSITION_TYPE:return object;default:switch(object=object&&object.$$typeof){case REACT_CONTEXT_TYPE:case REACT_FORWARD_REF_TYPE:case REACT_LAZY_TYPE:case REACT_MEMO_TYPE:case REACT_CONSUMER_TYPE:return object;default:return $$typeof}}case REACT_PORTAL_TYPE:return $$typeof}}}var REACT_ELEMENT_TYPE=Symbol.for("react.transitional.element"),REACT_PORTAL_TYPE=Symbol.for("react.portal"),REACT_FRAGMENT_TYPE=Symbol.for("react.fragment"),REACT_STRICT_MODE_TYPE=Symbol.for("react.strict_mode"),REACT_PROFILER_TYPE=Symbol.for("react.profiler");Symbol.for("react.provider");var REACT_CONSUMER_TYPE=Symbol.for("react.consumer"),REACT_CONTEXT_TYPE=Symbol.for("react.context"),REACT_FORWARD_REF_TYPE=Symbol.for("react.forward_ref"),REACT_SUSPENSE_TYPE=Symbol.for("react.suspense"),REACT_SUSPENSE_LIST_TYPE=Symbol.for("react.suspense_list"),REACT_MEMO_TYPE=Symbol.for("react.memo"),REACT_LAZY_TYPE=Symbol.for("react.lazy"),REACT_VIEW_TRANSITION_TYPE=Symbol.for("react.view_transition"),REACT_CLIENT_REFERENCE=Symbol.for("react.client.reference");exports.ContextConsumer=REACT_CONSUMER_TYPE,exports.ContextProvider=REACT_CONTEXT_TYPE,exports.Element=REACT_ELEMENT_TYPE,exports.ForwardRef=REACT_FORWARD_REF_TYPE,exports.Fragment=REACT_FRAGMENT_TYPE,exports.Lazy=REACT_LAZY_TYPE,exports.Memo=REACT_MEMO_TYPE,exports.Portal=REACT_PORTAL_TYPE,exports.Profiler=REACT_PROFILER_TYPE,exports.StrictMode=REACT_STRICT_MODE_TYPE,exports.Suspense=REACT_SUSPENSE_TYPE,exports.SuspenseList=REACT_SUSPENSE_LIST_TYPE,exports.isContextConsumer=function(object){return typeOf(object)===REACT_CONSUMER_TYPE},exports.isContextProvider=function(object){return typeOf(object)===REACT_CONTEXT_TYPE},exports.isElement=function(object){return"object"==typeof object&&null!==object&&object.$$typeof===REACT_ELEMENT_TYPE},exports.isForwardRef=function(object){return typeOf(object)===REACT_FORWARD_REF_TYPE},exports.isFragment=function(object){return typeOf(object)===REACT_FRAGMENT_TYPE},exports.isLazy=function(object){return typeOf(object)===REACT_LAZY_TYPE},exports.isMemo=function(object){return typeOf(object)===REACT_MEMO_TYPE},exports.isPortal=function(object){return typeOf(object)===REACT_PORTAL_TYPE},exports.isProfiler=function(object){return typeOf(object)===REACT_PROFILER_TYPE},exports.isStrictMode=function(object){return typeOf(object)===REACT_STRICT_MODE_TYPE},exports.isSuspense=function(object){return typeOf(object)===REACT_SUSPENSE_TYPE},exports.isSuspenseList=function(object){return typeOf(object)===REACT_SUSPENSE_LIST_TYPE},exports.isValidElementType=function(type){return"string"==typeof type||"function"==typeof type||type===REACT_FRAGMENT_TYPE||type===REACT_PROFILER_TYPE||type===REACT_STRICT_MODE_TYPE||type===REACT_SUSPENSE_TYPE||type===REACT_SUSPENSE_LIST_TYPE||"object"==typeof type&&null!==type&&(type.$$typeof===REACT_LAZY_TYPE||type.$$typeof===REACT_MEMO_TYPE||type.$$typeof===REACT_CONTEXT_TYPE||type.$$typeof===REACT_CONSUMER_TYPE||type.$$typeof===REACT_FORWARD_REF_TYPE||type.$$typeof===REACT_CLIENT_REFERENCE||void 0!==type.getModuleId)},exports.typeOf=typeOf}()},"./dist/compiled/react-is/index.js":/*!*****************************************!*\
  !*** ./dist/compiled/react-is/index.js ***!
  \*****************************************/(module1,__unused_webpack_exports,__webpack_require__)=>{"use strict";module1.exports=__webpack_require__(/*! ./cjs/react-is.development.js */"./dist/compiled/react-is/cjs/react-is.development.js")},"./dist/compiled/strip-ansi/index.js":/*!*******************************************!*\
  !*** ./dist/compiled/strip-ansi/index.js ***!
  \*******************************************/module1=>{(()=>{"use strict";var e={511:e=>{e.exports=({onlyFirst:e=!1}={})=>RegExp("[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)|(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))",e?void 0:"g")},532:(e,r,_)=>{let t=_(511);e.exports=e=>"string"==typeof e?e.replace(t(),""):e}},r={};function __nccwpck_require__1(_){var t=r[_];if(void 0!==t)return t.exports;var a=r[_]={exports:{}},n=!0;try{e[_](a,a.exports,__nccwpck_require__1),n=!1}finally{n&&delete r[_]}return a.exports}__nccwpck_require__1.ab=__dirname+"/";var _=__nccwpck_require__1(532);module1.exports=_})()},"./dist/esm/build/output/log.js":/*!**************************************************!*\
  !*** ./dist/esm/build/output/log.js + 2 modules ***!
  \**************************************************/(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";var _globalThis;__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{bootstrap:()=>bootstrap,error:()=>error,event:()=>log_event,info:()=>info,prefixes:()=>prefixes,ready:()=>ready,trace:()=>trace,wait:()=>wait,warn:()=>warn,warnOnce:()=>warnOnce});let{env,stdout}=(null==(_globalThis=globalThis)?void 0:_globalThis.process)??{},enabled=env&&!env.NO_COLOR&&(env.FORCE_COLOR||(null==stdout?void 0:stdout.isTTY)&&!env.CI&&"dumb"!==env.TERM),replaceClose=(str,close,replace,index)=>{let start=str.substring(0,index)+replace,end=str.substring(index+close.length),nextIndex=end.indexOf(close);return~nextIndex?start+replaceClose(end,close,replace,nextIndex):start+end},formatter=(open,close,replace=open)=>enabled?input=>{let string=""+input,index=string.indexOf(close,open.length);return~index?open+replaceClose(string,close,replace,index)+close:open+string+close}:String,bold=formatter("\x1b[1m","\x1b[22m","\x1b[22m\x1b[1m");formatter("\x1b[2m","\x1b[22m","\x1b[22m\x1b[2m"),formatter("\x1b[3m","\x1b[23m"),formatter("\x1b[4m","\x1b[24m"),formatter("\x1b[7m","\x1b[27m"),formatter("\x1b[8m","\x1b[28m"),formatter("\x1b[9m","\x1b[29m"),formatter("\x1b[30m","\x1b[39m");let red=formatter("\x1b[31m","\x1b[39m"),green=formatter("\x1b[32m","\x1b[39m"),yellow=formatter("\x1b[33m","\x1b[39m");formatter("\x1b[34m","\x1b[39m");let magenta=formatter("\x1b[35m","\x1b[39m");formatter("\x1b[38;2;173;127;168m","\x1b[39m"),formatter("\x1b[36m","\x1b[39m");let white=formatter("\x1b[37m","\x1b[39m");formatter("\x1b[90m","\x1b[39m"),formatter("\x1b[40m","\x1b[49m"),formatter("\x1b[41m","\x1b[49m"),formatter("\x1b[42m","\x1b[49m"),formatter("\x1b[43m","\x1b[49m"),formatter("\x1b[44m","\x1b[49m"),formatter("\x1b[45m","\x1b[49m"),formatter("\x1b[46m","\x1b[49m"),formatter("\x1b[47m","\x1b[49m");let prefixes={wait:white(bold("○")),error:red(bold("⨯")),warn:yellow(bold("⚠")),ready:"▲",info:white(bold(" ")),event:green(bold("✓")),trace:magenta(bold("»"))},LOGGING_METHOD={log:"log",warn:"warn",error:"error"};function prefixedLog(prefixType,...message){(""===message[0]||void 0===message[0])&&1===message.length&&message.shift();let consoleMethod=prefixType in LOGGING_METHOD?LOGGING_METHOD[prefixType]:"log",prefix=prefixes[prefixType];0===message.length?console[consoleMethod](""):1===message.length&&"string"==typeof message[0]?console[consoleMethod](" "+prefix+" "+message[0]):console[consoleMethod](" "+prefix,...message)}function bootstrap(...message){console.log("   "+message.join(" "))}function wait(...message){prefixedLog("wait",...message)}function error(...message){prefixedLog("error",...message)}function warn(...message){prefixedLog("warn",...message)}function ready(...message){prefixedLog("ready",...message)}function info(...message){prefixedLog("info",...message)}function log_event(...message){prefixedLog("event",...message)}function trace(...message){prefixedLog("trace",...message)}let warnOnceCache=new class{constructor(maxSize,calculateSize){this.cache=new Map,this.sizes=new Map,this.totalSize=0,this.maxSize=maxSize,this.calculateSize=calculateSize||(()=>1)}set(key,value){if(!key||!value)return;let size=this.calculateSize(value);if(size>this.maxSize){console.warn("Single item size exceeds maxSize");return}this.cache.has(key)&&(this.totalSize-=this.sizes.get(key)||0),this.cache.set(key,value),this.sizes.set(key,size),this.totalSize+=size,this.touch(key)}has(key){return!!key&&(this.touch(key),!!this.cache.get(key))}get(key){if(!key)return;let value=this.cache.get(key);if(void 0!==value)return this.touch(key),value}touch(key){let value=this.cache.get(key);void 0!==value&&(this.cache.delete(key),this.cache.set(key,value),this.evictIfNecessary())}evictIfNecessary(){for(;this.totalSize>this.maxSize&&this.cache.size>0;)this.evictLeastRecentlyUsed()}evictLeastRecentlyUsed(){let lruKey=this.cache.keys().next().value;if(void 0!==lruKey){let lruSize=this.sizes.get(lruKey)||0;this.totalSize-=lruSize,this.cache.delete(lruKey),this.sizes.delete(lruKey)}}reset(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}keys(){return[...this.cache.keys()]}remove(key){this.cache.has(key)&&(this.totalSize-=this.sizes.get(key)||0,this.cache.delete(key),this.sizes.delete(key))}clear(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}get size(){return this.cache.size}get currentSize(){return this.totalSize}}(1e4,value=>value.length);function warnOnce(...message){let key=message.join(" ");warnOnceCache.has(key)||(warnOnceCache.set(key,key),warn(...message))}},"./dist/esm/lib/constants.js":/*!***********************************!*\
  !*** ./dist/esm/lib/constants.js ***!
  \***********************************/(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{ACTION_SUFFIX:()=>ACTION_SUFFIX,APP_DIR_ALIAS:()=>APP_DIR_ALIAS,CACHE_ONE_YEAR:()=>CACHE_ONE_YEAR,DOT_NEXT_ALIAS:()=>DOT_NEXT_ALIAS,ESLINT_DEFAULT_DIRS:()=>ESLINT_DEFAULT_DIRS,GSP_NO_RETURNED_VALUE:()=>GSP_NO_RETURNED_VALUE,GSSP_COMPONENT_MEMBER_ERROR:()=>GSSP_COMPONENT_MEMBER_ERROR,GSSP_NO_RETURNED_VALUE:()=>GSSP_NO_RETURNED_VALUE,INFINITE_CACHE:()=>INFINITE_CACHE,INSTRUMENTATION_HOOK_FILENAME:()=>INSTRUMENTATION_HOOK_FILENAME,MATCHED_PATH_HEADER:()=>MATCHED_PATH_HEADER,MIDDLEWARE_FILENAME:()=>MIDDLEWARE_FILENAME,MIDDLEWARE_LOCATION_REGEXP:()=>MIDDLEWARE_LOCATION_REGEXP,NEXT_BODY_SUFFIX:()=>NEXT_BODY_SUFFIX,NEXT_CACHE_IMPLICIT_TAG_ID:()=>NEXT_CACHE_IMPLICIT_TAG_ID,NEXT_CACHE_REVALIDATED_TAGS_HEADER:()=>NEXT_CACHE_REVALIDATED_TAGS_HEADER,NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER:()=>NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,NEXT_CACHE_SOFT_TAG_MAX_LENGTH:()=>NEXT_CACHE_SOFT_TAG_MAX_LENGTH,NEXT_CACHE_TAGS_HEADER:()=>NEXT_CACHE_TAGS_HEADER,NEXT_CACHE_TAG_MAX_ITEMS:()=>NEXT_CACHE_TAG_MAX_ITEMS,NEXT_CACHE_TAG_MAX_LENGTH:()=>NEXT_CACHE_TAG_MAX_LENGTH,NEXT_DATA_SUFFIX:()=>NEXT_DATA_SUFFIX,NEXT_INTERCEPTION_MARKER_PREFIX:()=>NEXT_INTERCEPTION_MARKER_PREFIX,NEXT_META_SUFFIX:()=>NEXT_META_SUFFIX,NEXT_QUERY_PARAM_PREFIX:()=>NEXT_QUERY_PARAM_PREFIX,NEXT_RESUME_HEADER:()=>NEXT_RESUME_HEADER,NON_STANDARD_NODE_ENV:()=>NON_STANDARD_NODE_ENV,PAGES_DIR_ALIAS:()=>PAGES_DIR_ALIAS,PRERENDER_REVALIDATE_HEADER:()=>PRERENDER_REVALIDATE_HEADER,PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER:()=>PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER,PUBLIC_DIR_MIDDLEWARE_CONFLICT:()=>PUBLIC_DIR_MIDDLEWARE_CONFLICT,ROOT_DIR_ALIAS:()=>ROOT_DIR_ALIAS,RSC_ACTION_CLIENT_WRAPPER_ALIAS:()=>RSC_ACTION_CLIENT_WRAPPER_ALIAS,RSC_ACTION_ENCRYPTION_ALIAS:()=>RSC_ACTION_ENCRYPTION_ALIAS,RSC_ACTION_PROXY_ALIAS:()=>RSC_ACTION_PROXY_ALIAS,RSC_ACTION_VALIDATE_ALIAS:()=>RSC_ACTION_VALIDATE_ALIAS,RSC_CACHE_WRAPPER_ALIAS:()=>RSC_CACHE_WRAPPER_ALIAS,RSC_MOD_REF_PROXY_ALIAS:()=>RSC_MOD_REF_PROXY_ALIAS,RSC_PREFETCH_SUFFIX:()=>RSC_PREFETCH_SUFFIX,RSC_SEGMENTS_DIR_SUFFIX:()=>RSC_SEGMENTS_DIR_SUFFIX,RSC_SEGMENT_SUFFIX:()=>RSC_SEGMENT_SUFFIX,RSC_SUFFIX:()=>RSC_SUFFIX,SERVER_PROPS_EXPORT_ERROR:()=>SERVER_PROPS_EXPORT_ERROR,SERVER_PROPS_GET_INIT_PROPS_CONFLICT:()=>SERVER_PROPS_GET_INIT_PROPS_CONFLICT,SERVER_PROPS_SSG_CONFLICT:()=>SERVER_PROPS_SSG_CONFLICT,SERVER_RUNTIME:()=>SERVER_RUNTIME,SSG_FALLBACK_EXPORT_ERROR:()=>SSG_FALLBACK_EXPORT_ERROR,SSG_GET_INITIAL_PROPS_CONFLICT:()=>SSG_GET_INITIAL_PROPS_CONFLICT,STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR:()=>STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR,UNSTABLE_REVALIDATE_RENAME_ERROR:()=>UNSTABLE_REVALIDATE_RENAME_ERROR,WEBPACK_LAYERS:()=>WEBPACK_LAYERS,WEBPACK_RESOURCE_QUERIES:()=>WEBPACK_RESOURCE_QUERIES});let NEXT_QUERY_PARAM_PREFIX="nxtP",NEXT_INTERCEPTION_MARKER_PREFIX="nxtI",MATCHED_PATH_HEADER="x-matched-path",PRERENDER_REVALIDATE_HEADER="x-prerender-revalidate",PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER="x-prerender-revalidate-if-generated",RSC_PREFETCH_SUFFIX=".prefetch.rsc",RSC_SEGMENTS_DIR_SUFFIX=".segments",RSC_SEGMENT_SUFFIX=".segment.rsc",RSC_SUFFIX=".rsc",ACTION_SUFFIX=".action",NEXT_DATA_SUFFIX=".json",NEXT_META_SUFFIX=".meta",NEXT_BODY_SUFFIX=".body",NEXT_CACHE_TAGS_HEADER="x-next-cache-tags",NEXT_CACHE_REVALIDATED_TAGS_HEADER="x-next-revalidated-tags",NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER="x-next-revalidate-tag-token",NEXT_RESUME_HEADER="next-resume",NEXT_CACHE_TAG_MAX_ITEMS=128,NEXT_CACHE_TAG_MAX_LENGTH=256,NEXT_CACHE_SOFT_TAG_MAX_LENGTH=1024,NEXT_CACHE_IMPLICIT_TAG_ID="_N_T_",CACHE_ONE_YEAR=31536e3,INFINITE_CACHE=0xfffffffe,MIDDLEWARE_FILENAME="middleware",MIDDLEWARE_LOCATION_REGEXP=`(?:src/)?${MIDDLEWARE_FILENAME}`,INSTRUMENTATION_HOOK_FILENAME="instrumentation",PAGES_DIR_ALIAS="private-next-pages",DOT_NEXT_ALIAS="private-dot-next",ROOT_DIR_ALIAS="private-next-root-dir",APP_DIR_ALIAS="private-next-app-dir",RSC_MOD_REF_PROXY_ALIAS="private-next-rsc-mod-ref-proxy",RSC_ACTION_VALIDATE_ALIAS="private-next-rsc-action-validate",RSC_ACTION_PROXY_ALIAS="private-next-rsc-server-reference",RSC_CACHE_WRAPPER_ALIAS="private-next-rsc-cache-wrapper",RSC_ACTION_ENCRYPTION_ALIAS="private-next-rsc-action-encryption",RSC_ACTION_CLIENT_WRAPPER_ALIAS="private-next-rsc-action-client-wrapper",PUBLIC_DIR_MIDDLEWARE_CONFLICT="You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict",SSG_GET_INITIAL_PROPS_CONFLICT="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",SERVER_PROPS_GET_INIT_PROPS_CONFLICT="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",SERVER_PROPS_SSG_CONFLICT="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",SERVER_PROPS_EXPORT_ERROR="pages with `getServerSideProps` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export",GSP_NO_RETURNED_VALUE="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",GSSP_NO_RETURNED_VALUE="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",UNSTABLE_REVALIDATE_RENAME_ERROR="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",GSSP_COMPONENT_MEMBER_ERROR="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",NON_STANDARD_NODE_ENV='You are using a non-standard "NODE_ENV" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env',SSG_FALLBACK_EXPORT_ERROR="Pages with `fallback` enabled in `getStaticPaths` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export",ESLINT_DEFAULT_DIRS=["app","pages","components","lib","src"],SERVER_RUNTIME={edge:"edge",experimentalEdge:"experimental-edge",nodejs:"nodejs"},WEBPACK_LAYERS_NAMES={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"},WEBPACK_LAYERS={...WEBPACK_LAYERS_NAMES,GROUP:{builtinReact:[WEBPACK_LAYERS_NAMES.reactServerComponents,WEBPACK_LAYERS_NAMES.actionBrowser],serverOnly:[WEBPACK_LAYERS_NAMES.reactServerComponents,WEBPACK_LAYERS_NAMES.actionBrowser,WEBPACK_LAYERS_NAMES.instrument,WEBPACK_LAYERS_NAMES.middleware],neutralTarget:[WEBPACK_LAYERS_NAMES.apiNode,WEBPACK_LAYERS_NAMES.apiEdge],clientOnly:[WEBPACK_LAYERS_NAMES.serverSideRendering,WEBPACK_LAYERS_NAMES.appPagesBrowser],bundled:[WEBPACK_LAYERS_NAMES.reactServerComponents,WEBPACK_LAYERS_NAMES.actionBrowser,WEBPACK_LAYERS_NAMES.serverSideRendering,WEBPACK_LAYERS_NAMES.appPagesBrowser,WEBPACK_LAYERS_NAMES.shared,WEBPACK_LAYERS_NAMES.instrument,WEBPACK_LAYERS_NAMES.middleware],appPages:[WEBPACK_LAYERS_NAMES.reactServerComponents,WEBPACK_LAYERS_NAMES.serverSideRendering,WEBPACK_LAYERS_NAMES.appPagesBrowser,WEBPACK_LAYERS_NAMES.actionBrowser]}},WEBPACK_RESOURCE_QUERIES={edgeSSREntry:"__next_edge_ssr_entry__",metadata:"__next_metadata__",metadataRoute:"__next_metadata_route__",metadataImageMeta:"__next_metadata_image_meta__"}},"./dist/esm/server/api-utils/index.js":/*!********************************************!*\
  !*** ./dist/esm/server/api-utils/index.js ***!
  \********************************************/(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{ApiError:()=>ApiError,COOKIE_NAME_PRERENDER_BYPASS:()=>COOKIE_NAME_PRERENDER_BYPASS,COOKIE_NAME_PRERENDER_DATA:()=>COOKIE_NAME_PRERENDER_DATA,RESPONSE_LIMIT_DEFAULT:()=>RESPONSE_LIMIT_DEFAULT,SYMBOL_CLEARED_COOKIES:()=>SYMBOL_CLEARED_COOKIES,SYMBOL_PREVIEW_DATA:()=>SYMBOL_PREVIEW_DATA,checkIsOnDemandRevalidate:()=>checkIsOnDemandRevalidate,clearPreviewData:()=>clearPreviewData,redirect:()=>redirect,sendError:()=>sendError,sendStatusCode:()=>sendStatusCode,setLazyProp:()=>setLazyProp,wrapApiHandler:()=>wrapApiHandler});var _web_spec_extension_adapters_headers__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__(/*! ../web/spec-extension/adapters/headers */"./dist/esm/server/web/spec-extension/adapters/headers.js"),_lib_constants__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__(/*! ../../lib/constants */"./dist/esm/lib/constants.js"),_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__(/*! ../lib/trace/tracer */"./lib/trace/tracer"),_lib_trace_constants__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__(/*! ../lib/trace/constants */"./dist/esm/server/lib/trace/constants.js");function wrapApiHandler(page,handler){return(...args)=>((0,_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_2__.getTracer)().setRootSpanAttribute("next.route",page),(0,_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_2__.getTracer)().trace(_lib_trace_constants__WEBPACK_IMPORTED_MODULE_3__.NodeSpan.runHandler,{spanName:`executing api route (pages) ${page}`},()=>handler(...args)))}function sendStatusCode(res,statusCode){return res.statusCode=statusCode,res}function redirect(res,statusOrUrl,url){if("string"==typeof statusOrUrl&&(url=statusOrUrl,statusOrUrl=307),"number"!=typeof statusOrUrl||"string"!=typeof url)throw Object.defineProperty(Error("Invalid redirect arguments. Please use a single argument URL, e.g. res.redirect('/destination') or use a status code and URL, e.g. res.redirect(307, '/destination')."),"__NEXT_ERROR_CODE",{value:"E389",enumerable:!1,configurable:!0});return res.writeHead(statusOrUrl,{Location:url}),res.write(url),res.end(),res}function checkIsOnDemandRevalidate(req,previewProps){let headers=_web_spec_extension_adapters_headers__WEBPACK_IMPORTED_MODULE_0__.HeadersAdapter.from(req.headers);return{isOnDemandRevalidate:headers.get(_lib_constants__WEBPACK_IMPORTED_MODULE_1__.PRERENDER_REVALIDATE_HEADER)===previewProps.previewModeId,revalidateOnlyGenerated:headers.has(_lib_constants__WEBPACK_IMPORTED_MODULE_1__.PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER)}}let COOKIE_NAME_PRERENDER_BYPASS="__prerender_bypass",COOKIE_NAME_PRERENDER_DATA="__next_preview_data",RESPONSE_LIMIT_DEFAULT=4194304,SYMBOL_PREVIEW_DATA=Symbol(COOKIE_NAME_PRERENDER_DATA),SYMBOL_CLEARED_COOKIES=Symbol(COOKIE_NAME_PRERENDER_BYPASS);function clearPreviewData(res,options={}){if(SYMBOL_CLEARED_COOKIES in res)return res;let{serialize}=__webpack_require__(/*! next/dist/compiled/cookie */"./dist/compiled/cookie/index.js"),previous=res.getHeader("Set-Cookie");return res.setHeader("Set-Cookie",[..."string"==typeof previous?[previous]:Array.isArray(previous)?previous:[],serialize(COOKIE_NAME_PRERENDER_BYPASS,"",{expires:new Date(0),httpOnly:!0,sameSite:"lax",secure:!1,path:"/",...void 0!==options.path?{path:options.path}:void 0}),serialize(COOKIE_NAME_PRERENDER_DATA,"",{expires:new Date(0),httpOnly:!0,sameSite:"lax",secure:!1,path:"/",...void 0!==options.path?{path:options.path}:void 0})]),Object.defineProperty(res,SYMBOL_CLEARED_COOKIES,{value:!0,enumerable:!1}),res}class ApiError extends Error{constructor(statusCode,message){super(message),this.statusCode=statusCode}}function sendError(res,statusCode,message){res.statusCode=statusCode,res.statusMessage=message,res.end(message)}function setLazyProp({req},prop,getter){let opts={configurable:!0,enumerable:!0},optsReset={...opts,writable:!0};Object.defineProperty(req,prop,{...opts,get:()=>{let value=getter();return Object.defineProperty(req,prop,{...optsReset,value}),value},set:value=>{Object.defineProperty(req,prop,{...optsReset,value})}})}},"./dist/esm/server/api-utils/node/try-get-preview-data.js":/*!****************************************************************!*\
  !*** ./dist/esm/server/api-utils/node/try-get-preview-data.js ***!
  \****************************************************************/(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{tryGetPreviewData:()=>tryGetPreviewData});var ___WEBPACK_IMPORTED_MODULE_0__=__webpack_require__(/*! ../index */"./dist/esm/server/api-utils/index.js"),_web_spec_extension_cookies__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__(/*! ../../web/spec-extension/cookies */"./dist/esm/server/web/spec-extension/cookies.js"),_web_spec_extension_adapters_headers__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__(/*! ../../web/spec-extension/adapters/headers */"./dist/esm/server/web/spec-extension/adapters/headers.js");function tryGetPreviewData(req,res,options,multiZoneDraftMode){var _cookies_get,_cookies_get1;let encryptedPreviewData;if(options&&(0,___WEBPACK_IMPORTED_MODULE_0__.checkIsOnDemandRevalidate)(req,options).isOnDemandRevalidate)return!1;if(___WEBPACK_IMPORTED_MODULE_0__.SYMBOL_PREVIEW_DATA in req)return req[___WEBPACK_IMPORTED_MODULE_0__.SYMBOL_PREVIEW_DATA];let headers=_web_spec_extension_adapters_headers__WEBPACK_IMPORTED_MODULE_2__.HeadersAdapter.from(req.headers),cookies=new _web_spec_extension_cookies__WEBPACK_IMPORTED_MODULE_1__.RequestCookies(headers),previewModeId=null==(_cookies_get=cookies.get(___WEBPACK_IMPORTED_MODULE_0__.COOKIE_NAME_PRERENDER_BYPASS))?void 0:_cookies_get.value,tokenPreviewData=null==(_cookies_get1=cookies.get(___WEBPACK_IMPORTED_MODULE_0__.COOKIE_NAME_PRERENDER_DATA))?void 0:_cookies_get1.value;if(previewModeId&&!tokenPreviewData&&previewModeId===options.previewModeId){let data={};return Object.defineProperty(req,___WEBPACK_IMPORTED_MODULE_0__.SYMBOL_PREVIEW_DATA,{value:data,enumerable:!1}),data}if(!previewModeId&&!tokenPreviewData)return!1;if(!previewModeId||!tokenPreviewData||previewModeId!==options.previewModeId)return multiZoneDraftMode||(0,___WEBPACK_IMPORTED_MODULE_0__.clearPreviewData)(res),!1;try{encryptedPreviewData=__webpack_require__(/*! next/dist/compiled/jsonwebtoken */"next/dist/compiled/jsonwebtoken").verify(tokenPreviewData,options.previewModeSigningKey)}catch{return(0,___WEBPACK_IMPORTED_MODULE_0__.clearPreviewData)(res),!1}let{decryptWithSecret}=__webpack_require__(/*! ../../crypto-utils */"./dist/esm/server/crypto-utils.js"),decryptedPreviewData=decryptWithSecret(Buffer.from(options.previewModeEncryptionKey),encryptedPreviewData.data);try{let data=JSON.parse(decryptedPreviewData);return Object.defineProperty(req,___WEBPACK_IMPORTED_MODULE_0__.SYMBOL_PREVIEW_DATA,{value:data,enumerable:!1}),data}catch{return!1}}},"./dist/esm/server/crypto-utils.js":/*!*****************************************************!*\
  !*** ./dist/esm/server/crypto-utils.js + 1 modules ***!
  \*****************************************************/(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{decryptWithSecret:()=>decryptWithSecret,encryptWithSecret:()=>encryptWithSecret});let external_crypto_namespaceObject=require("crypto");var external_crypto_default=/*#__PURE__*/__webpack_require__.n(external_crypto_namespaceObject);let CIPHER_ALGORITHM="aes-256-gcm";function encryptWithSecret(secret,data){let iv=external_crypto_default().randomBytes(16),salt=external_crypto_default().randomBytes(64),key=external_crypto_default().pbkdf2Sync(secret,salt,1e5,32,"sha512"),cipher=external_crypto_default().createCipheriv(CIPHER_ALGORITHM,key,iv),encrypted=Buffer.concat([cipher.update(data,"utf8"),cipher.final()]),tag=cipher.getAuthTag();return Buffer.concat([salt,iv,tag,encrypted]).toString("hex")}function decryptWithSecret(secret,encryptedData){let buffer=Buffer.from(encryptedData,"hex"),salt=buffer.slice(0,64),iv=buffer.slice(64,80),tag=buffer.slice(80,96),encrypted=buffer.slice(96),key=external_crypto_default().pbkdf2Sync(secret,salt,1e5,32,"sha512"),decipher=external_crypto_default().createDecipheriv(CIPHER_ALGORITHM,key,iv);return decipher.setAuthTag(tag),decipher.update(encrypted)+decipher.final("utf8")}},"./dist/esm/server/lib/trace/constants.js":/*!************************************************!*\
  !*** ./dist/esm/server/lib/trace/constants.js ***!
  \************************************************/(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{AppRenderSpan:()=>AppRenderSpan,AppRouteRouteHandlersSpan:()=>AppRouteRouteHandlersSpan,BaseServerSpan:()=>BaseServerSpan,LoadComponentsSpan:()=>LoadComponentsSpan,LogSpanAllowList:()=>LogSpanAllowList,MiddlewareSpan:()=>MiddlewareSpan,NextNodeServerSpan:()=>NextNodeServerSpan,NextServerSpan:()=>NextServerSpan,NextVanillaSpanAllowlist:()=>NextVanillaSpanAllowlist,NodeSpan:()=>NodeSpan,RenderSpan:()=>RenderSpan,ResolveMetadataSpan:()=>ResolveMetadataSpan,RouterSpan:()=>RouterSpan,StartServerSpan:()=>StartServerSpan});var BaseServerSpan=/*#__PURE__*/function(BaseServerSpan){return BaseServerSpan.handleRequest="BaseServer.handleRequest",BaseServerSpan.run="BaseServer.run",BaseServerSpan.pipe="BaseServer.pipe",BaseServerSpan.getStaticHTML="BaseServer.getStaticHTML",BaseServerSpan.render="BaseServer.render",BaseServerSpan.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",BaseServerSpan.renderToResponse="BaseServer.renderToResponse",BaseServerSpan.renderToHTML="BaseServer.renderToHTML",BaseServerSpan.renderError="BaseServer.renderError",BaseServerSpan.renderErrorToResponse="BaseServer.renderErrorToResponse",BaseServerSpan.renderErrorToHTML="BaseServer.renderErrorToHTML",BaseServerSpan.render404="BaseServer.render404",BaseServerSpan}(BaseServerSpan||{}),LoadComponentsSpan=/*#__PURE__*/function(LoadComponentsSpan){return LoadComponentsSpan.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",LoadComponentsSpan.loadComponents="LoadComponents.loadComponents",LoadComponentsSpan}(LoadComponentsSpan||{}),NextServerSpan=/*#__PURE__*/function(NextServerSpan){return NextServerSpan.getRequestHandler="NextServer.getRequestHandler",NextServerSpan.getServer="NextServer.getServer",NextServerSpan.getServerRequestHandler="NextServer.getServerRequestHandler",NextServerSpan.createServer="createServer.createServer",NextServerSpan}(NextServerSpan||{}),NextNodeServerSpan=/*#__PURE__*/function(NextNodeServerSpan){return NextNodeServerSpan.compression="NextNodeServer.compression",NextNodeServerSpan.getBuildId="NextNodeServer.getBuildId",NextNodeServerSpan.createComponentTree="NextNodeServer.createComponentTree",NextNodeServerSpan.clientComponentLoading="NextNodeServer.clientComponentLoading",NextNodeServerSpan.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",NextNodeServerSpan.generateStaticRoutes="NextNodeServer.generateStaticRoutes",NextNodeServerSpan.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",NextNodeServerSpan.generatePublicRoutes="NextNodeServer.generatePublicRoutes",NextNodeServerSpan.generateImageRoutes="NextNodeServer.generateImageRoutes.route",NextNodeServerSpan.sendRenderResult="NextNodeServer.sendRenderResult",NextNodeServerSpan.proxyRequest="NextNodeServer.proxyRequest",NextNodeServerSpan.runApi="NextNodeServer.runApi",NextNodeServerSpan.render="NextNodeServer.render",NextNodeServerSpan.renderHTML="NextNodeServer.renderHTML",NextNodeServerSpan.imageOptimizer="NextNodeServer.imageOptimizer",NextNodeServerSpan.getPagePath="NextNodeServer.getPagePath",NextNodeServerSpan.getRoutesManifest="NextNodeServer.getRoutesManifest",NextNodeServerSpan.findPageComponents="NextNodeServer.findPageComponents",NextNodeServerSpan.getFontManifest="NextNodeServer.getFontManifest",NextNodeServerSpan.getServerComponentManifest="NextNodeServer.getServerComponentManifest",NextNodeServerSpan.getRequestHandler="NextNodeServer.getRequestHandler",NextNodeServerSpan.renderToHTML="NextNodeServer.renderToHTML",NextNodeServerSpan.renderError="NextNodeServer.renderError",NextNodeServerSpan.renderErrorToHTML="NextNodeServer.renderErrorToHTML",NextNodeServerSpan.render404="NextNodeServer.render404",NextNodeServerSpan.startResponse="NextNodeServer.startResponse",NextNodeServerSpan.route="route",NextNodeServerSpan.onProxyReq="onProxyReq",NextNodeServerSpan.apiResolver="apiResolver",NextNodeServerSpan.internalFetch="internalFetch",NextNodeServerSpan}(NextNodeServerSpan||{}),StartServerSpan=/*#__PURE__*/function(StartServerSpan){return StartServerSpan.startServer="startServer.startServer",StartServerSpan}(StartServerSpan||{}),RenderSpan=/*#__PURE__*/function(RenderSpan){return RenderSpan.getServerSideProps="Render.getServerSideProps",RenderSpan.getStaticProps="Render.getStaticProps",RenderSpan.renderToString="Render.renderToString",RenderSpan.renderDocument="Render.renderDocument",RenderSpan.createBodyResult="Render.createBodyResult",RenderSpan}(RenderSpan||{}),AppRenderSpan=/*#__PURE__*/function(AppRenderSpan){return AppRenderSpan.renderToString="AppRender.renderToString",AppRenderSpan.renderToReadableStream="AppRender.renderToReadableStream",AppRenderSpan.getBodyResult="AppRender.getBodyResult",AppRenderSpan.fetch="AppRender.fetch",AppRenderSpan}(AppRenderSpan||{}),RouterSpan=/*#__PURE__*/function(RouterSpan){return RouterSpan.executeRoute="Router.executeRoute",RouterSpan}(RouterSpan||{}),NodeSpan=/*#__PURE__*/function(NodeSpan){return NodeSpan.runHandler="Node.runHandler",NodeSpan}(NodeSpan||{}),AppRouteRouteHandlersSpan=/*#__PURE__*/function(AppRouteRouteHandlersSpan){return AppRouteRouteHandlersSpan.runHandler="AppRouteRouteHandlers.runHandler",AppRouteRouteHandlersSpan}(AppRouteRouteHandlersSpan||{}),ResolveMetadataSpan=/*#__PURE__*/function(ResolveMetadataSpan){return ResolveMetadataSpan.generateMetadata="ResolveMetadata.generateMetadata",ResolveMetadataSpan.generateViewport="ResolveMetadata.generateViewport",ResolveMetadataSpan}(ResolveMetadataSpan||{}),MiddlewareSpan=/*#__PURE__*/function(MiddlewareSpan){return MiddlewareSpan.execute="Middleware.execute",MiddlewareSpan}(MiddlewareSpan||{});let NextVanillaSpanAllowlist=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],LogSpanAllowList=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"]},"./dist/esm/server/post-process.js":/*!*****************************************************!*\
  !*** ./dist/esm/server/post-process.js + 1 modules ***!
  \*****************************************************/(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";function nonNullable(value){return null!=value}async function postProcessHTML(pathname,content,renderOpts,{inAmpMode,hybridAmp}){for(let postProcessor of[null,(0,renderOpts.optimizeCss)?async html=>{let cssOptimizer=new(__webpack_require__(/*! critters */"critters"))({ssrMode:!0,reduceInlineStyles:!1,path:renderOpts.distDir,publicPath:`${renderOpts.assetPrefix}/_next/`,preload:"media",fonts:!1,logLevel:process.env.CRITTERS_LOG_LEVEL||"info",...renderOpts.optimizeCss});return await cssOptimizer.process(html)}:null,inAmpMode||hybridAmp?html=>html.replace(/&amp;amp=1/g,"&amp=1"):null].filter(nonNullable))postProcessor&&(content=await postProcessor(content));return content}__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{postProcessHTML:()=>postProcessHTML})},"./dist/esm/server/web/spec-extension/adapters/headers.js":/*!****************************************************************!*\
  !*** ./dist/esm/server/web/spec-extension/adapters/headers.js ***!
  \****************************************************************/(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{HeadersAdapter:()=>HeadersAdapter,ReadonlyHeadersError:()=>ReadonlyHeadersError});var _reflect__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__(/*! ./reflect */"./dist/esm/server/web/spec-extension/adapters/reflect.js");class ReadonlyHeadersError extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new ReadonlyHeadersError}}class HeadersAdapter extends Headers{constructor(headers){super(),this.headers=new Proxy(headers,{get(target,prop,receiver){if("symbol"==typeof prop)return _reflect__WEBPACK_IMPORTED_MODULE_0__.ReflectAdapter.get(target,prop,receiver);let lowercased=prop.toLowerCase(),original=Object.keys(headers).find(o=>o.toLowerCase()===lowercased);if(void 0!==original)return _reflect__WEBPACK_IMPORTED_MODULE_0__.ReflectAdapter.get(target,original,receiver)},set(target,prop,value,receiver){if("symbol"==typeof prop)return _reflect__WEBPACK_IMPORTED_MODULE_0__.ReflectAdapter.set(target,prop,value,receiver);let lowercased=prop.toLowerCase(),original=Object.keys(headers).find(o=>o.toLowerCase()===lowercased);return _reflect__WEBPACK_IMPORTED_MODULE_0__.ReflectAdapter.set(target,original??prop,value,receiver)},has(target,prop){if("symbol"==typeof prop)return _reflect__WEBPACK_IMPORTED_MODULE_0__.ReflectAdapter.has(target,prop);let lowercased=prop.toLowerCase(),original=Object.keys(headers).find(o=>o.toLowerCase()===lowercased);return void 0!==original&&_reflect__WEBPACK_IMPORTED_MODULE_0__.ReflectAdapter.has(target,original)},deleteProperty(target,prop){if("symbol"==typeof prop)return _reflect__WEBPACK_IMPORTED_MODULE_0__.ReflectAdapter.deleteProperty(target,prop);let lowercased=prop.toLowerCase(),original=Object.keys(headers).find(o=>o.toLowerCase()===lowercased);return void 0===original||_reflect__WEBPACK_IMPORTED_MODULE_0__.ReflectAdapter.deleteProperty(target,original)}})}static seal(headers){return new Proxy(headers,{get(target,prop,receiver){switch(prop){case"append":case"delete":case"set":return ReadonlyHeadersError.callable;default:return _reflect__WEBPACK_IMPORTED_MODULE_0__.ReflectAdapter.get(target,prop,receiver)}}})}merge(value){return Array.isArray(value)?value.join(", "):value}static from(headers){return headers instanceof Headers?headers:new HeadersAdapter(headers)}append(name,value){let existing=this.headers[name];"string"==typeof existing?this.headers[name]=[existing,value]:Array.isArray(existing)?existing.push(value):this.headers[name]=value}delete(name){delete this.headers[name]}get(name){let value=this.headers[name];return void 0!==value?this.merge(value):null}has(name){return void 0!==this.headers[name]}set(name,value){this.headers[name]=value}forEach(callbackfn,thisArg){for(let[name,value]of this.entries())callbackfn.call(thisArg,value,name,this)}*entries(){for(let key of Object.keys(this.headers)){let name=key.toLowerCase(),value=this.get(name);yield[name,value]}}*keys(){for(let key of Object.keys(this.headers)){let name=key.toLowerCase();yield name}}*values(){for(let key of Object.keys(this.headers)){let value=this.get(key);yield value}}[Symbol.iterator](){return this.entries()}}},"./dist/esm/server/web/spec-extension/adapters/reflect.js":/*!****************************************************************!*\
  !*** ./dist/esm/server/web/spec-extension/adapters/reflect.js ***!
  \****************************************************************/(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{ReflectAdapter:()=>ReflectAdapter});class ReflectAdapter{static get(target,prop,receiver){let value=Reflect.get(target,prop,receiver);return"function"==typeof value?value.bind(target):value}static set(target,prop,value,receiver){return Reflect.set(target,prop,value,receiver)}static has(target,prop){return Reflect.has(target,prop)}static deleteProperty(target,prop){return Reflect.deleteProperty(target,prop)}}},"./dist/esm/server/web/spec-extension/cookies.js":/*!*******************************************************!*\
  !*** ./dist/esm/server/web/spec-extension/cookies.js ***!
  \*******************************************************/(__unused_webpack_module,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{RequestCookies:()=>next_dist_compiled_edge_runtime_cookies__WEBPACK_IMPORTED_MODULE_0__.RequestCookies,ResponseCookies:()=>next_dist_compiled_edge_runtime_cookies__WEBPACK_IMPORTED_MODULE_0__.ResponseCookies,stringifyCookie:()=>next_dist_compiled_edge_runtime_cookies__WEBPACK_IMPORTED_MODULE_0__.stringifyCookie});var next_dist_compiled_edge_runtime_cookies__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__(/*! next/dist/compiled/@edge-runtime/cookies */"./dist/compiled/@edge-runtime/cookies/index.js")},"./dist/esm/shared/lib/modern-browserslist-target.js":/*!***********************************************************!*\
  !*** ./dist/esm/shared/lib/modern-browserslist-target.js ***!
  \***********************************************************/module1=>{module1.exports=["chrome 64","edge 79","firefox 67","opera 51","safari 12"]},"./dist/server/ReactDOMServerPages.js":/*!********************************************!*\
  !*** ./dist/server/ReactDOMServerPages.js ***!
  \********************************************/(module1,__unused_webpack_exports,__webpack_require__)=>{"use strict";let ReactDOMServer;try{ReactDOMServer=__webpack_require__(/*! react-dom/server.edge */"react-dom/server.edge")}catch(error){if("MODULE_NOT_FOUND"!==error.code&&"ERR_PACKAGE_PATH_NOT_EXPORTED"!==error.code)throw error;ReactDOMServer=__webpack_require__(/*! react-dom/server.browser */"react-dom/server.browser")}module1.exports=ReactDOMServer},"./lib/trace/tracer":/*!****************************************************!*\
  !*** external "next/dist/server/lib/trace/tracer" ***!
  \****************************************************/module1=>{"use strict";module1.exports=require("next/dist/server/lib/trace/tracer")},critters:/*!***************************!*\
  !*** external "critters" ***!
  \***************************/module1=>{"use strict";module1.exports=require("critters")},"next/dist/compiled/jsonwebtoken":/*!**************************************************!*\
  !*** external "next/dist/compiled/jsonwebtoken" ***!
  \**************************************************/module1=>{"use strict";module1.exports=require("next/dist/compiled/jsonwebtoken")},path:/*!***********************!*\
  !*** external "path" ***!
  \***********************/module1=>{"use strict";module1.exports=require("path")},"react-dom/server.browser":/*!*******************************************!*\
  !*** external "react-dom/server.browser" ***!
  \*******************************************/module1=>{"use strict";module1.exports=require("react-dom/server.browser")},"react-dom/server.edge":/*!****************************************!*\
  !*** external "react-dom/server.edge" ***!
  \****************************************/module1=>{"use strict";module1.exports=require("react-dom/server.edge")}},__webpack_module_cache__={};function __webpack_require__(moduleId){var cachedModule=__webpack_module_cache__[moduleId];if(void 0!==cachedModule)return cachedModule.exports;var module1=__webpack_module_cache__[moduleId]={exports:{}};return __webpack_modules__[moduleId](module1,module1.exports,__webpack_require__),module1.exports}__webpack_require__.n=module1=>{var getter=module1&&module1.__esModule?()=>module1.default:()=>module1;return __webpack_require__.d(getter,{a:getter}),getter},__webpack_require__.d=(exports,definition)=>{for(var key in definition)__webpack_require__.o(definition,key)&&!__webpack_require__.o(exports,key)&&Object.defineProperty(exports,key,{enumerable:!0,get:definition[key]})},__webpack_require__.o=(obj,prop)=>Object.prototype.hasOwnProperty.call(obj,prop),__webpack_require__.r=exports=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(exports,"__esModule",{value:!0})};var __webpack_exports__={};(()=>{"use strict";let tryGetPreviewData,warn,postProcessHTML;/*!********************************************************************!*\
  !*** ./dist/esm/server/route-modules/pages/module.js + 75 modules ***!
  \********************************************************************/__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{PagesRouteModule:()=>PagesRouteModule,default:()=>pages_module,renderToHTML:()=>renderToHTML,vendored:()=>vendored});var amp_context_shared_runtime_namespaceObject={};__webpack_require__.r(amp_context_shared_runtime_namespaceObject),__webpack_require__.d(amp_context_shared_runtime_namespaceObject,{AmpStateContext:()=>AmpStateContext});var head_manager_context_shared_runtime_namespaceObject={};__webpack_require__.r(head_manager_context_shared_runtime_namespaceObject),__webpack_require__.d(head_manager_context_shared_runtime_namespaceObject,{HeadManagerContext:()=>HeadManagerContext});var loadable_context_shared_runtime_namespaceObject={};__webpack_require__.r(loadable_context_shared_runtime_namespaceObject),__webpack_require__.d(loadable_context_shared_runtime_namespaceObject,{LoadableContext:()=>LoadableContext});var loadable_shared_runtime_namespaceObject={};__webpack_require__.r(loadable_shared_runtime_namespaceObject),__webpack_require__.d(loadable_shared_runtime_namespaceObject,{default:()=>loadable_shared_runtime});var router_context_shared_runtime_namespaceObject={};__webpack_require__.r(router_context_shared_runtime_namespaceObject),__webpack_require__.d(router_context_shared_runtime_namespaceObject,{RouterContext:()=>RouterContext});var html_context_shared_runtime_namespaceObject={};__webpack_require__.r(html_context_shared_runtime_namespaceObject),__webpack_require__.d(html_context_shared_runtime_namespaceObject,{HtmlContext:()=>HtmlContext,useHtmlContext:()=>useHtmlContext});var image_config_context_shared_runtime_namespaceObject={};__webpack_require__.r(image_config_context_shared_runtime_namespaceObject),__webpack_require__.d(image_config_context_shared_runtime_namespaceObject,{ImageConfigContext:()=>ImageConfigContext});var hooks_client_context_shared_runtime_namespaceObject={};__webpack_require__.r(hooks_client_context_shared_runtime_namespaceObject),__webpack_require__.d(hooks_client_context_shared_runtime_namespaceObject,{PathParamsContext:()=>PathParamsContext,PathnameContext:()=>PathnameContext,SearchParamsContext:()=>SearchParamsContext});var app_router_context_shared_runtime_namespaceObject={};__webpack_require__.r(app_router_context_shared_runtime_namespaceObject),__webpack_require__.d(app_router_context_shared_runtime_namespaceObject,{AppRouterContext:()=>AppRouterContext,GlobalLayoutRouterContext:()=>GlobalLayoutRouterContext,LayoutRouterContext:()=>LayoutRouterContext,MissingSlotContext:()=>MissingSlotContext,TemplateContext:()=>TemplateContext});var server_inserted_html_shared_runtime_namespaceObject={};__webpack_require__.r(server_inserted_html_shared_runtime_namespaceObject),__webpack_require__.d(server_inserted_html_shared_runtime_namespaceObject,{ServerInsertedHTMLContext:()=>ServerInsertedHTMLContext,useServerInsertedHTML:()=>useServerInsertedHTML});var entrypoints_namespaceObject={};__webpack_require__.r(entrypoints_namespaceObject),__webpack_require__.d(entrypoints_namespaceObject,{AmpContext:()=>amp_context_shared_runtime_namespaceObject,AppRouterContext:()=>app_router_context_shared_runtime_namespaceObject,HeadManagerContext:()=>head_manager_context_shared_runtime_namespaceObject,HooksClientContext:()=>hooks_client_context_shared_runtime_namespaceObject,HtmlContext:()=>html_context_shared_runtime_namespaceObject,ImageConfigContext:()=>image_config_context_shared_runtime_namespaceObject,Loadable:()=>loadable_shared_runtime_namespaceObject,LoadableContext:()=>loadable_context_shared_runtime_namespaceObject,RouterContext:()=>router_context_shared_runtime_namespaceObject,ServerInsertedHtml:()=>server_inserted_html_shared_runtime_namespaceObject});class RouteModule{constructor({userland,definition}){this.userland=userland,this.definition=definition}}let jsx_runtime_namespaceObject=require("react/jsx-runtime");var api_utils=__webpack_require__("./dist/esm/server/api-utils/index.js");let external_react_namespaceObject=require("react");var external_react_default=/*#__PURE__*/__webpack_require__.n(external_react_namespaceObject),ReactDOMServerPages=__webpack_require__("./dist/server/ReactDOMServerPages.js"),ReactDOMServerPages_default=/*#__PURE__*/__webpack_require__.n(ReactDOMServerPages);let external_styled_jsx_namespaceObject=require("styled-jsx");var constants=__webpack_require__("./dist/esm/lib/constants.js");__webpack_require__("./dist/esm/shared/lib/modern-browserslist-target.js");let COMPILER_NAMES={client:"client",server:"server",edgeServer:"edge-server"};COMPILER_NAMES.client,COMPILER_NAMES.server,COMPILER_NAMES.edgeServer,Symbol("polyfills");let STATIC_STATUS_PAGES=["/500"];function getObjectClassLabel(value){return Object.prototype.toString.call(value)}function isPlainObject(value){if("[object Object]"!==getObjectClassLabel(value))return!1;let prototype=Object.getPrototypeOf(value);return null===prototype||prototype.hasOwnProperty("isPrototypeOf")}let regexpPlainIdentifier=/^[A-Za-z_$][A-Za-z0-9_$]*$/;class SerializableError extends Error{constructor(page,method,path,message){super(path?`Error serializing \`${path}\` returned from \`${method}\` in "${page}".
Reason: ${message}`:`Error serializing props returned from \`${method}\` in "${page}".
Reason: ${message}`)}}function isSerializableProps(page,method,input){if(!isPlainObject(input))throw Object.defineProperty(new SerializableError(page,method,"",`Props must be returned as a plain object from ${method}: \`{ props: { ... } }\` (received: \`${getObjectClassLabel(input)}\`).`),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});function visit(visited,value,path){if(visited.has(value))throw Object.defineProperty(new SerializableError(page,method,path,`Circular references cannot be expressed in JSON (references: \`${visited.get(value)||"(self)"}\`).`),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});visited.set(value,path)}return function isSerializable(refs,value,path){let type=typeof value;if(null===value||"boolean"===type||"number"===type||"string"===type)return!0;if("undefined"===type)throw Object.defineProperty(new SerializableError(page,method,path,"`undefined` cannot be serialized as JSON. Please use `null` or omit this value."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});if(isPlainObject(value)){if(visit(refs,value,path),Object.entries(value).every(([key,nestedValue])=>{let nextPath=regexpPlainIdentifier.test(key)?`${path}.${key}`:`${path}[${JSON.stringify(key)}]`,newRefs=new Map(refs);return isSerializable(newRefs,key,nextPath)&&isSerializable(newRefs,nestedValue,nextPath)}))return!0;throw Object.defineProperty(new SerializableError(page,method,path,"invariant: Unknown error encountered in Object."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}if(Array.isArray(value)){if(visit(refs,value,path),value.every((nestedValue,index)=>isSerializable(new Map(refs),nestedValue,`${path}[${index}]`)))return!0;throw Object.defineProperty(new SerializableError(page,method,path,"invariant: Unknown error encountered in Array."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}throw Object.defineProperty(new SerializableError(page,method,path,"`"+type+"`"+("object"===type?` ("${Object.prototype.toString.call(value)}")`:"")+" cannot be serialized as JSON. Please only return JSON serializable data types."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}(new Map,input,"")}let AmpStateContext=external_react_default().createContext({});AmpStateContext.displayName="AmpStateContext";let HeadManagerContext=external_react_default().createContext({});HeadManagerContext.displayName="HeadManagerContext";let LoadableContext=external_react_default().createContext(null);LoadableContext.displayName="LoadableContext";let ALL_INITIALIZERS=[],READY_INITIALIZERS=[];function load(loader){let promise=loader(),state={loading:!0,loaded:null,error:null};return state.promise=promise.then(loaded=>(state.loading=!1,state.loaded=loaded,loaded)).catch(err=>{throw state.loading=!1,state.error=err,err}),state}class LoadableSubscription{promise(){return this._res.promise}retry(){this._clearTimeouts(),this._res=this._loadFn(this._opts.loader),this._state={pastDelay:!1,timedOut:!1};let{_res:res,_opts:opts}=this;res.loading&&("number"==typeof opts.delay&&(0===opts.delay?this._state.pastDelay=!0:this._delay=setTimeout(()=>{this._update({pastDelay:!0})},opts.delay)),"number"==typeof opts.timeout&&(this._timeout=setTimeout(()=>{this._update({timedOut:!0})},opts.timeout))),this._res.promise.then(()=>{this._update({}),this._clearTimeouts()}).catch(_err=>{this._update({}),this._clearTimeouts()}),this._update({})}_update(partial){this._state={...this._state,error:this._res.error,loaded:this._res.loaded,loading:this._res.loading,...partial},this._callbacks.forEach(callback=>callback())}_clearTimeouts(){clearTimeout(this._delay),clearTimeout(this._timeout)}getCurrentValue(){return this._state}subscribe(callback){return this._callbacks.add(callback),()=>{this._callbacks.delete(callback)}}constructor(loadFn,opts){this._loadFn=loadFn,this._opts=opts,this._callbacks=new Set,this._delay=null,this._timeout=null,this.retry()}}function Loadable(opts){return function(loadFn,options){let opts=Object.assign({loader:null,loading:null,delay:200,timeout:null,webpack:null,modules:null},options),subscription=null;function init(){if(!subscription){let sub=new LoadableSubscription(loadFn,opts);subscription={getCurrentValue:sub.getCurrentValue.bind(sub),subscribe:sub.subscribe.bind(sub),retry:sub.retry.bind(sub),promise:sub.promise.bind(sub)}}return subscription.promise()}function LoadableComponent(props,ref){!function(){init();let context=external_react_default().useContext(LoadableContext);context&&Array.isArray(opts.modules)&&opts.modules.forEach(moduleName=>{context(moduleName)})}();let state=external_react_default().useSyncExternalStore(subscription.subscribe,subscription.getCurrentValue,subscription.getCurrentValue);return external_react_default().useImperativeHandle(ref,()=>({retry:subscription.retry}),[]),external_react_default().useMemo(()=>{var obj;return state.loading||state.error?/*#__PURE__*/external_react_default().createElement(opts.loading,{isLoading:state.loading,pastDelay:state.pastDelay,timedOut:state.timedOut,error:state.error,retry:subscription.retry}):state.loaded?/*#__PURE__*/external_react_default().createElement((obj=state.loaded)&&obj.default?obj.default:obj,props):null},[props,state])}return ALL_INITIALIZERS.push(init),LoadableComponent.preload=()=>init(),LoadableComponent.displayName="LoadableComponent",/*#__PURE__*/external_react_default().forwardRef(LoadableComponent)}(load,opts)}function flushInitializers(initializers,ids){let promises=[];for(;initializers.length;){let init=initializers.pop();promises.push(init(ids))}return Promise.all(promises).then(()=>{if(initializers.length)return flushInitializers(initializers,ids)})}Loadable.preloadAll=()=>new Promise((resolveInitializers,reject)=>{flushInitializers(ALL_INITIALIZERS).then(resolveInitializers,reject)}),Loadable.preloadReady=ids=>(void 0===ids&&(ids=[]),new Promise(resolvePreload=>{let res=()=>resolvePreload();flushInitializers(READY_INITIALIZERS,ids).then(res,res)}));let loadable_shared_runtime=Loadable,RouterContext=external_react_default().createContext(null);function ensureLeadingSlash(path){return path.startsWith("/")?path:"/"+path}RouterContext.displayName="RouterContext";let INTERCEPTION_ROUTE_MARKERS=["(..)(..)","(.)","(..)","(...)"],TEST_ROUTE=/\/[^/]*\[[^/]+\][^/]*(?=\/|$)/,TEST_STRICT_ROUTE=/\/\[[^/]+\](?=\/|$)/;function isDynamicRoute(route,strict){return void 0===strict&&(strict=!0),void 0!==route.split("/").find(segment=>INTERCEPTION_ROUTE_MARKERS.find(m=>segment.startsWith(m)))&&(route=function(path){let interceptingRoute,marker,interceptedRoute;for(let segment of path.split("/"))if(marker=INTERCEPTION_ROUTE_MARKERS.find(m=>segment.startsWith(m))){[interceptingRoute,interceptedRoute]=path.split(marker,2);break}if(!interceptingRoute||!marker||!interceptedRoute)throw Object.defineProperty(Error("Invalid interception route: "+path+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(interceptingRoute=ensureLeadingSlash(interceptingRoute.split("/").reduce((pathname,segment,index,segments)=>segment?"("===segment[0]&&segment.endsWith(")")||"@"===segment[0]||("page"===segment||"route"===segment)&&index===segments.length-1?pathname:pathname+"/"+segment:pathname,"")),marker){case"(.)":interceptedRoute="/"===interceptingRoute?"/"+interceptedRoute:interceptingRoute+"/"+interceptedRoute;break;case"(..)":if("/"===interceptingRoute)throw Object.defineProperty(Error("Invalid interception route: "+path+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});interceptedRoute=interceptingRoute.split("/").slice(0,-1).concat(interceptedRoute).join("/");break;case"(...)":interceptedRoute="/"+interceptedRoute;break;case"(..)(..)":let splitInterceptingRoute=interceptingRoute.split("/");if(splitInterceptingRoute.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+path+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});interceptedRoute=splitInterceptingRoute.slice(0,-2).concat(interceptedRoute).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute,interceptedRoute}}(route).interceptedRoute),strict?TEST_STRICT_ROUTE.test(route):TEST_ROUTE.test(route)}function getDisplayName(Component){return"string"==typeof Component?Component:Component.displayName||Component.name||"Unknown"}function isResSent(res){return res.finished||res.headersSent}async function loadGetInitialProps(App,ctx){var _App_prototype;if(null==(_App_prototype=App.prototype)?void 0:_App_prototype.getInitialProps)throw Object.defineProperty(Error('"'+getDisplayName(App)+'.getInitialProps()" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});let res=ctx.res||ctx.ctx&&ctx.ctx.res;if(!App.getInitialProps)return ctx.ctx&&ctx.Component?{pageProps:await loadGetInitialProps(ctx.Component,ctx.ctx)}:{};let props=await App.getInitialProps(ctx);if(res&&isResSent(res))return props;if(!props)throw Object.defineProperty(Error('"'+getDisplayName(App)+'.getInitialProps()" should resolve to an object. But found "'+props+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return 0!==Object.keys(props).length||ctx.ctx||console.warn(""+getDisplayName(App)+" returned an empty object from `getInitialProps`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps"),props}"undefined"!=typeof performance&&["mark","measure","getEntriesByName"].every(method=>"function"==typeof performance[method]);class NormalizeError extends Error{}let HtmlContext=(0,external_react_namespaceObject.createContext)(void 0);function useHtmlContext(){let context=(0,external_react_namespaceObject.useContext)(HtmlContext);if(!context)throw Object.defineProperty(Error("<Html> should not be imported outside of pages/_document.\nRead more: https://nextjs.org/docs/messages/no-document-import-in-page"),"__NEXT_ERROR_CODE",{value:"E67",enumerable:!1,configurable:!0});return context}HtmlContext.displayName="HtmlContext";class UrlNode{insert(urlPath){this._insert(urlPath.split("/").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(prefix){void 0===prefix&&(prefix="/");let childrenPaths=[...this.children.keys()].sort();null!==this.slugName&&childrenPaths.splice(childrenPaths.indexOf("[]"),1),null!==this.restSlugName&&childrenPaths.splice(childrenPaths.indexOf("[...]"),1),null!==this.optionalRestSlugName&&childrenPaths.splice(childrenPaths.indexOf("[[...]]"),1);let routes=childrenPaths.map(c=>this.children.get(c)._smoosh(""+prefix+c+"/")).reduce((prev,curr)=>[...prev,...curr],[]);if(null!==this.slugName&&routes.push(...this.children.get("[]")._smoosh(prefix+"["+this.slugName+"]/")),!this.placeholder){let r="/"===prefix?"/":prefix.slice(0,-1);if(null!=this.optionalRestSlugName)throw Object.defineProperty(Error('You cannot define a route with the same specificity as a optional catch-all route ("'+r+'" and "'+r+"[[..."+this.optionalRestSlugName+']]").'),"__NEXT_ERROR_CODE",{value:"E458",enumerable:!1,configurable:!0});routes.unshift(r)}return null!==this.restSlugName&&routes.push(...this.children.get("[...]")._smoosh(prefix+"[..."+this.restSlugName+"]/")),null!==this.optionalRestSlugName&&routes.push(...this.children.get("[[...]]")._smoosh(prefix+"[[..."+this.optionalRestSlugName+"]]/")),routes}_insert(urlPaths,slugNames,isCatchAll){if(0===urlPaths.length){this.placeholder=!1;return}if(isCatchAll)throw Object.defineProperty(Error("Catch-all must be the last part of the URL."),"__NEXT_ERROR_CODE",{value:"E392",enumerable:!1,configurable:!0});let nextSegment=urlPaths[0];if(nextSegment.startsWith("[")&&nextSegment.endsWith("]")){let segmentName=nextSegment.slice(1,-1),isOptional=!1;if(segmentName.startsWith("[")&&segmentName.endsWith("]")&&(segmentName=segmentName.slice(1,-1),isOptional=!0),segmentName.startsWith("…"))throw Object.defineProperty(Error("Detected a three-dot character ('…') at ('"+segmentName+"'). Did you mean ('...')?"),"__NEXT_ERROR_CODE",{value:"E147",enumerable:!1,configurable:!0});if(segmentName.startsWith("...")&&(segmentName=segmentName.substring(3),isCatchAll=!0),segmentName.startsWith("[")||segmentName.endsWith("]"))throw Object.defineProperty(Error("Segment names may not start or end with extra brackets ('"+segmentName+"')."),"__NEXT_ERROR_CODE",{value:"E421",enumerable:!1,configurable:!0});if(segmentName.startsWith("."))throw Object.defineProperty(Error("Segment names may not start with erroneous periods ('"+segmentName+"')."),"__NEXT_ERROR_CODE",{value:"E288",enumerable:!1,configurable:!0});function handleSlug(previousSlug,nextSlug){if(null!==previousSlug&&previousSlug!==nextSlug)throw Object.defineProperty(Error("You cannot use different slug names for the same dynamic path ('"+previousSlug+"' !== '"+nextSlug+"')."),"__NEXT_ERROR_CODE",{value:"E337",enumerable:!1,configurable:!0});slugNames.forEach(slug=>{if(slug===nextSlug)throw Object.defineProperty(Error('You cannot have the same slug name "'+nextSlug+'" repeat within a single dynamic path'),"__NEXT_ERROR_CODE",{value:"E247",enumerable:!1,configurable:!0});if(slug.replace(/\W/g,"")===nextSegment.replace(/\W/g,""))throw Object.defineProperty(Error('You cannot have the slug names "'+slug+'" and "'+nextSlug+'" differ only by non-word symbols within a single dynamic path'),"__NEXT_ERROR_CODE",{value:"E499",enumerable:!1,configurable:!0})}),slugNames.push(nextSlug)}if(isCatchAll){if(isOptional){if(null!=this.restSlugName)throw Object.defineProperty(Error('You cannot use both an required and optional catch-all route at the same level ("[...'+this.restSlugName+']" and "'+urlPaths[0]+'" ).'),"__NEXT_ERROR_CODE",{value:"E299",enumerable:!1,configurable:!0});handleSlug(this.optionalRestSlugName,segmentName),this.optionalRestSlugName=segmentName,nextSegment="[[...]]"}else{if(null!=this.optionalRestSlugName)throw Object.defineProperty(Error('You cannot use both an optional and required catch-all route at the same level ("[[...'+this.optionalRestSlugName+']]" and "'+urlPaths[0]+'").'),"__NEXT_ERROR_CODE",{value:"E300",enumerable:!1,configurable:!0});handleSlug(this.restSlugName,segmentName),this.restSlugName=segmentName,nextSegment="[...]"}}else{if(isOptional)throw Object.defineProperty(Error('Optional route parameters are not yet supported ("'+urlPaths[0]+'").'),"__NEXT_ERROR_CODE",{value:"E435",enumerable:!1,configurable:!0});handleSlug(this.slugName,segmentName),this.slugName=segmentName,nextSegment="[]"}}this.children.has(nextSegment)||this.children.set(nextSegment,new UrlNode),this.children.get(nextSegment)._insert(urlPaths.slice(1),slugNames,isCatchAll)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}}let NEXT_REQUEST_META=Symbol.for("NextInternalRequestMeta");function getRequestMeta(req,key){let meta=req[NEXT_REQUEST_META]||{};return"string"==typeof key?meta[key]:meta}var RedirectStatusCode=/*#__PURE__*/function(RedirectStatusCode){return RedirectStatusCode[RedirectStatusCode.SeeOther=303]="SeeOther",RedirectStatusCode[RedirectStatusCode.TemporaryRedirect=307]="TemporaryRedirect",RedirectStatusCode[RedirectStatusCode.PermanentRedirect=308]="PermanentRedirect",RedirectStatusCode}({});let allowedStatusCodes=new Set([301,302,303,307,308]);function getRedirectStatus(route){return route.statusCode||(route.permanent?RedirectStatusCode.PermanentRedirect:RedirectStatusCode.TemporaryRedirect)}var tracer_=__webpack_require__("./lib/trace/tracer"),trace_constants=__webpack_require__("./dist/esm/server/lib/trace/constants.js");class DetachedPromise{constructor(){let resolve,reject;this.promise=new Promise((res,rej)=>{resolve=res,reject=rej}),this.resolve=resolve,this.reject=reject}}function voidCatch(){}new Uint8Array([60,104,116,109,108]),new Uint8Array([60,98,111,100,121]),new Uint8Array([60,47,104,101,97,100,62]),new Uint8Array([60,47,98,111,100,121,62]),new Uint8Array([60,47,104,116,109,108,62]),new Uint8Array([60,47,98,111,100,121,62,60,47,104,116,109,108,62]);let encoder=new TextEncoder;function streamFromBuffer(chunk){return new ReadableStream({start(controller){controller.enqueue(chunk),controller.close()}})}async function streamToBuffer(stream){let reader=stream.getReader(),chunks=[];for(;;){let{done,value}=await reader.read();if(done)break;chunks.push(value)}return Buffer.concat(chunks)}async function streamToString(stream,signal){let decoder=new TextDecoder("utf-8",{fatal:!0}),string="";for await(let chunk of stream){if(null==signal?void 0:signal.aborted)return string;string+=decoder.decode(chunk,{stream:!0})}return string+decoder.decode()}function removeTrailingSlash(route){return route.replace(/\/$/,"")||"/"}function parsePath(path){let hashIndex=path.indexOf("#"),queryIndex=path.indexOf("?"),hasQuery=queryIndex>-1&&(hashIndex<0||queryIndex<hashIndex);return hasQuery||hashIndex>-1?{pathname:path.substring(0,hasQuery?queryIndex:hashIndex),query:hasQuery?path.substring(queryIndex,hashIndex>-1?hashIndex:void 0):"",hash:hashIndex>-1?path.slice(hashIndex):""}:{pathname:path,query:"",hash:""}}function addPathPrefix(path,prefix){if(!path.startsWith("/")||!prefix)return path;let{pathname,query,hash}=parsePath(path);return""+prefix+pathname+query+hash}function addPathSuffix(path,suffix){if(!path.startsWith("/")||!suffix)return path;let{pathname,query,hash}=parsePath(path);return""+pathname+suffix+query+hash}function pathHasPrefix(path,prefix){if("string"!=typeof path)return!1;let{pathname}=parsePath(path);return pathname===prefix||pathname.startsWith(prefix+"/")}let cache=new WeakMap;function normalizeLocalePath(pathname,locales){let detectedLocale;if(!locales)return{pathname};let lowercasedLocales=cache.get(locales);lowercasedLocales||(lowercasedLocales=locales.map(locale=>locale.toLowerCase()),cache.set(locales,lowercasedLocales));let segments=pathname.split("/",2);if(!segments[1])return{pathname};let segment=segments[1].toLowerCase(),index=lowercasedLocales.indexOf(segment);return index<0?{pathname}:(detectedLocale=locales[index],{pathname:pathname=pathname.slice(detectedLocale.length+1)||"/",detectedLocale})}let REGEX_LOCALHOST_HOSTNAME=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function parseURL(url,base){return new URL(String(url).replace(REGEX_LOCALHOST_HOSTNAME,"localhost"),base&&String(base).replace(REGEX_LOCALHOST_HOSTNAME,"localhost"))}let Internal=Symbol("NextURLInternal");class NextURL{constructor(input,baseOrOpts,opts){let base,options;"object"==typeof baseOrOpts&&"pathname"in baseOrOpts||"string"==typeof baseOrOpts?(base=baseOrOpts,options=opts||{}):options=opts||baseOrOpts||{},this[Internal]={url:parseURL(input,base??options.base),options:options,basePath:""},this.analyze()}analyze(){var _this_Internal_options_nextConfig_i18n,_this_Internal_options_nextConfig,_this_Internal_domainLocale,_this_Internal_options_nextConfig_i18n1,_this_Internal_options_nextConfig1;let info=function(pathname,options){var _options_nextConfig,_result_pathname;let{basePath,i18n,trailingSlash}=null!=(_options_nextConfig=options.nextConfig)?_options_nextConfig:{},info={pathname,trailingSlash:"/"!==pathname?pathname.endsWith("/"):trailingSlash};basePath&&pathHasPrefix(info.pathname,basePath)&&(info.pathname=function(path,prefix){if(!pathHasPrefix(path,prefix))return path;let withoutPrefix=path.slice(prefix.length);return withoutPrefix.startsWith("/")?withoutPrefix:"/"+withoutPrefix}(info.pathname,basePath),info.basePath=basePath);let pathnameNoDataPrefix=info.pathname;if(info.pathname.startsWith("/_next/data/")&&info.pathname.endsWith(".json")){let paths=info.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/"),buildId=paths[0];info.buildId=buildId,pathnameNoDataPrefix="index"!==paths[1]?"/"+paths.slice(1).join("/"):"/",!0===options.parseData&&(info.pathname=pathnameNoDataPrefix)}if(i18n){let result=options.i18nProvider?options.i18nProvider.analyze(info.pathname):normalizeLocalePath(info.pathname,i18n.locales);info.locale=result.detectedLocale,info.pathname=null!=(_result_pathname=result.pathname)?_result_pathname:info.pathname,!result.detectedLocale&&info.buildId&&(result=options.i18nProvider?options.i18nProvider.analyze(pathnameNoDataPrefix):normalizeLocalePath(pathnameNoDataPrefix,i18n.locales)).detectedLocale&&(info.locale=result.detectedLocale)}return info}(this[Internal].url.pathname,{nextConfig:this[Internal].options.nextConfig,parseData:!process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE,i18nProvider:this[Internal].options.i18nProvider}),hostname=function(parsed,headers){let hostname;if((null==headers?void 0:headers.host)&&!Array.isArray(headers.host))hostname=headers.host.toString().split(":",1)[0];else{if(!parsed.hostname)return;hostname=parsed.hostname}return hostname.toLowerCase()}(this[Internal].url,this[Internal].options.headers);this[Internal].domainLocale=this[Internal].options.i18nProvider?this[Internal].options.i18nProvider.detectDomainLocale(hostname):function(domainItems,hostname,detectedLocale){if(domainItems)for(let item of(detectedLocale&&(detectedLocale=detectedLocale.toLowerCase()),domainItems)){var _item_domain,_item_locales;if(hostname===(null==(_item_domain=item.domain)?void 0:_item_domain.split(":",1)[0].toLowerCase())||detectedLocale===item.defaultLocale.toLowerCase()||(null==(_item_locales=item.locales)?void 0:_item_locales.some(locale=>locale.toLowerCase()===detectedLocale)))return item}}(null==(_this_Internal_options_nextConfig=this[Internal].options.nextConfig)?void 0:null==(_this_Internal_options_nextConfig_i18n=_this_Internal_options_nextConfig.i18n)?void 0:_this_Internal_options_nextConfig_i18n.domains,hostname);let defaultLocale=(null==(_this_Internal_domainLocale=this[Internal].domainLocale)?void 0:_this_Internal_domainLocale.defaultLocale)||(null==(_this_Internal_options_nextConfig1=this[Internal].options.nextConfig)?void 0:null==(_this_Internal_options_nextConfig_i18n1=_this_Internal_options_nextConfig1.i18n)?void 0:_this_Internal_options_nextConfig_i18n1.defaultLocale);this[Internal].url.pathname=info.pathname,this[Internal].defaultLocale=defaultLocale,this[Internal].basePath=info.basePath??"",this[Internal].buildId=info.buildId,this[Internal].locale=info.locale??defaultLocale,this[Internal].trailingSlash=info.trailingSlash}formatPathname(){var info;let pathname;return pathname=function(path,locale,defaultLocale,ignorePrefix){if(!locale||locale===defaultLocale)return path;let lower=path.toLowerCase();return!ignorePrefix&&(pathHasPrefix(lower,"/api")||pathHasPrefix(lower,"/"+locale.toLowerCase()))?path:addPathPrefix(path,"/"+locale)}((info={basePath:this[Internal].basePath,buildId:this[Internal].buildId,defaultLocale:this[Internal].options.forceLocale?void 0:this[Internal].defaultLocale,locale:this[Internal].locale,pathname:this[Internal].url.pathname,trailingSlash:this[Internal].trailingSlash}).pathname,info.locale,info.buildId?void 0:info.defaultLocale,info.ignorePrefix),(info.buildId||!info.trailingSlash)&&(pathname=removeTrailingSlash(pathname)),info.buildId&&(pathname=addPathSuffix(addPathPrefix(pathname,"/_next/data/"+info.buildId),"/"===info.pathname?"index.json":".json")),pathname=addPathPrefix(pathname,info.basePath),!info.buildId&&info.trailingSlash?pathname.endsWith("/")?pathname:addPathSuffix(pathname,"/"):removeTrailingSlash(pathname)}formatSearch(){return this[Internal].url.search}get buildId(){return this[Internal].buildId}set buildId(buildId){this[Internal].buildId=buildId}get locale(){return this[Internal].locale??""}set locale(locale){var _this_Internal_options_nextConfig_i18n,_this_Internal_options_nextConfig;if(!this[Internal].locale||!(null==(_this_Internal_options_nextConfig=this[Internal].options.nextConfig)?void 0:null==(_this_Internal_options_nextConfig_i18n=_this_Internal_options_nextConfig.i18n)?void 0:_this_Internal_options_nextConfig_i18n.locales.includes(locale)))throw Object.defineProperty(TypeError(`The NextURL configuration includes no locale "${locale}"`),"__NEXT_ERROR_CODE",{value:"E597",enumerable:!1,configurable:!0});this[Internal].locale=locale}get defaultLocale(){return this[Internal].defaultLocale}get domainLocale(){return this[Internal].domainLocale}get searchParams(){return this[Internal].url.searchParams}get host(){return this[Internal].url.host}set host(value){this[Internal].url.host=value}get hostname(){return this[Internal].url.hostname}set hostname(value){this[Internal].url.hostname=value}get port(){return this[Internal].url.port}set port(value){this[Internal].url.port=value}get protocol(){return this[Internal].url.protocol}set protocol(value){this[Internal].url.protocol=value}get href(){let pathname=this.formatPathname(),search=this.formatSearch();return`${this.protocol}//${this.host}${pathname}${search}${this.hash}`}set href(url){this[Internal].url=parseURL(url),this.analyze()}get origin(){return this[Internal].url.origin}get pathname(){return this[Internal].url.pathname}set pathname(value){this[Internal].url.pathname=value}get hash(){return this[Internal].url.hash}set hash(value){this[Internal].url.hash=value}get search(){return this[Internal].url.search}set search(value){this[Internal].url.search=value}get password(){return this[Internal].url.password}set password(value){this[Internal].url.password=value}get username(){return this[Internal].url.username}set username(value){this[Internal].url.username=value}get basePath(){return this[Internal].basePath}set basePath(value){this[Internal].basePath=value.startsWith("/")?value:`/${value}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new NextURL(String(this),this[Internal].options)}}__webpack_require__("./dist/esm/server/web/spec-extension/cookies.js"),Symbol("internal request"),Request,Symbol.for("edge-runtime.inspect.custom");let ResponseAbortedName="ResponseAborted";class ResponseAborted extends Error{constructor(...args){super(...args),this.name=ResponseAbortedName}}let clientComponentLoadStart=0,clientComponentLoadTimes=0,clientComponentLoadCount=0;function isAbortError(e){return(null==e?void 0:e.name)==="AbortError"||(null==e?void 0:e.name)===ResponseAbortedName}async function pipeToNodeResponse(readable,res,waitUntilForEnd){try{let{errored,destroyed}=res;if(errored||destroyed)return;let controller=function(response){let controller=new AbortController;return response.once("close",()=>{response.writableFinished||controller.abort(new ResponseAborted)}),controller}(res),writer=function(res,waitUntilForEnd){let started=!1,drained=new DetachedPromise;function onDrain(){drained.resolve()}res.on("drain",onDrain),res.once("close",()=>{res.off("drain",onDrain),drained.resolve()});let finished=new DetachedPromise;return res.once("finish",()=>{finished.resolve()}),new WritableStream({write:async chunk=>{if(!started){if(started=!0,"performance"in globalThis&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX){let metrics=function(options={}){let metrics=0===clientComponentLoadStart?void 0:{clientComponentLoadStart,clientComponentLoadTimes,clientComponentLoadCount};return options.reset&&(clientComponentLoadStart=0,clientComponentLoadTimes=0,clientComponentLoadCount=0),metrics}();metrics&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-client-component-loading`,{start:metrics.clientComponentLoadStart,end:metrics.clientComponentLoadStart+metrics.clientComponentLoadTimes})}res.flushHeaders(),(0,tracer_.getTracer)().trace(trace_constants.NextNodeServerSpan.startResponse,{spanName:"start response"},()=>void 0)}try{let ok=res.write(chunk);"flush"in res&&"function"==typeof res.flush&&res.flush(),ok||(await drained.promise,drained=new DetachedPromise)}catch(err){throw res.end(),Object.defineProperty(Error("failed to write chunk to response",{cause:err}),"__NEXT_ERROR_CODE",{value:"E321",enumerable:!1,configurable:!0})}},abort:err=>{res.writableFinished||res.destroy(err)},close:async()=>{if(waitUntilForEnd&&await waitUntilForEnd,!res.writableFinished)return res.end(),finished.promise}})}(res,waitUntilForEnd);await readable.pipeTo(writer,{signal:controller.signal})}catch(err){if(isAbortError(err))return;throw Object.defineProperty(Error("failed to pipe response",{cause:err}),"__NEXT_ERROR_CODE",{value:"E180",enumerable:!1,configurable:!0})}}class RenderResult{static fromStatic(value){return new RenderResult(value,{metadata:{}})}constructor(response,{contentType,waitUntil,metadata}){this.response=response,this.contentType=contentType,this.metadata=metadata,this.waitUntil=waitUntil}assignMetadata(metadata){Object.assign(this.metadata,metadata)}get isNull(){return null===this.response}get isDynamic(){return"string"!=typeof this.response}toUnchunkedBuffer(stream=!1){if(null===this.response)throw Object.defineProperty(Error("Invariant: null responses cannot be unchunked"),"__NEXT_ERROR_CODE",{value:"E274",enumerable:!1,configurable:!0});if("string"!=typeof this.response){if(!stream)throw Object.defineProperty(Error("Invariant: dynamic responses cannot be unchunked. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E81",enumerable:!1,configurable:!0});return streamToBuffer(this.readable)}return Buffer.from(this.response)}toUnchunkedString(stream=!1){if(null===this.response)throw Object.defineProperty(Error("Invariant: null responses cannot be unchunked"),"__NEXT_ERROR_CODE",{value:"E274",enumerable:!1,configurable:!0});if("string"!=typeof this.response){if(!stream)throw Object.defineProperty(Error("Invariant: dynamic responses cannot be unchunked. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E81",enumerable:!1,configurable:!0});return streamToString(this.readable)}return this.response}get readable(){if(null===this.response)throw Object.defineProperty(Error("Invariant: null responses cannot be streamed"),"__NEXT_ERROR_CODE",{value:"E14",enumerable:!1,configurable:!0});if("string"==typeof this.response)throw Object.defineProperty(Error("Invariant: static responses cannot be streamed"),"__NEXT_ERROR_CODE",{value:"E151",enumerable:!1,configurable:!0});return Buffer.isBuffer(this.response)?streamFromBuffer(this.response):Array.isArray(this.response)?function(...streams){if(0===streams.length)throw Object.defineProperty(Error("Invariant: chainStreams requires at least one stream"),"__NEXT_ERROR_CODE",{value:"E437",enumerable:!1,configurable:!0});if(1===streams.length)return streams[0];let{readable,writable}=new TransformStream,promise=streams[0].pipeTo(writable,{preventClose:!0}),i=1;for(;i<streams.length-1;i++){let nextStream=streams[i];promise=promise.then(()=>nextStream.pipeTo(writable,{preventClose:!0}))}let lastStream=streams[i];return(promise=promise.then(()=>lastStream.pipeTo(writable))).catch(voidCatch),readable}(...this.response):this.response}chain(readable){var str;let responses;if(null===this.response)throw Object.defineProperty(Error("Invariant: response is null. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E258",enumerable:!1,configurable:!0});"string"==typeof this.response?responses=[(str=this.response,new ReadableStream({start(controller){controller.enqueue(encoder.encode(str)),controller.close()}}))]:Array.isArray(this.response)?responses=this.response:Buffer.isBuffer(this.response)?responses=[streamFromBuffer(this.response)]:responses=[this.response],responses.push(readable),this.response=responses}async pipeTo(writable){try{await this.readable.pipeTo(writable,{preventClose:!0}),this.waitUntil&&await this.waitUntil,await writable.close()}catch(err){if(isAbortError(err)){await writable.abort(err);return}throw err}}async pipeToNodeResponse(res){await pipeToNodeResponse(this.readable,res,this.waitUntil)}}let ImageConfigContext=external_react_default().createContext({deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1});ImageConfigContext.displayName="ImageConfigContext";var strip_ansi=__webpack_require__("./dist/compiled/strip-ansi/index.js"),strip_ansi_default=/*#__PURE__*/__webpack_require__.n(strip_ansi);let INTERNAL_QUERY_NAMES=["_rsc"],SearchParamsContext=(0,external_react_namespaceObject.createContext)(null),PathnameContext=(0,external_react_namespaceObject.createContext)(null),PathParamsContext=(0,external_react_namespaceObject.createContext)(null);SearchParamsContext.displayName="SearchParamsContext",PathnameContext.displayName="PathnameContext",PathParamsContext.displayName="PathParamsContext";let reHasRegExp=/[|\\{}()[\]^$+*?.-]/,reReplaceRegExp=/[|\\{}()[\]^$+*?.-]/g;function escapeStringRegexp(str){return reHasRegExp.test(str)?str.replace(reReplaceRegExp,"\\$&"):str}let PARAMETER_PATTERN=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function parseMatchedParameter(param){let optional=param.startsWith("[")&&param.endsWith("]");optional&&(param=param.slice(1,-1));let repeat=param.startsWith("...");return repeat&&(param=param.slice(3)),{key:param,repeat,optional}}function PathnameContextProviderAdapter(param){let{children,router,...props}=param,ref=(0,external_react_namespaceObject.useRef)(props.isAutoExport),value=(0,external_react_namespaceObject.useMemo)(()=>{let url;let isAutoExport=ref.current;if(isAutoExport&&(ref.current=!1),isDynamicRoute(router.pathname)&&(router.isFallback||isAutoExport&&!router.isReady))return null;try{url=new URL(router.asPath,"http://f")}catch(_){return"/"}return url.pathname},[router.asPath,router.isFallback,router.isReady,router.pathname]);return/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsx)(PathnameContext.Provider,{value:value,children:children})}let AppRouterContext=external_react_default().createContext(null),LayoutRouterContext=external_react_default().createContext(null),GlobalLayoutRouterContext=external_react_default().createContext(null),TemplateContext=external_react_default().createContext(null);AppRouterContext.displayName="AppRouterContext",LayoutRouterContext.displayName="LayoutRouterContext",GlobalLayoutRouterContext.displayName="GlobalLayoutRouterContext",TemplateContext.displayName="TemplateContext";let MissingSlotContext=external_react_default().createContext(new Set);var reflect=__webpack_require__("./dist/esm/server/web/spec-extension/adapters/reflect.js");let symbolError=Symbol.for("NextjsError"),DOCTYPE="<!DOCTYPE html>";function noRouter(){throw Object.defineProperty(Error('No router instance found. you should only use "next/router" inside the client side of your app. https://nextjs.org/docs/messages/no-router-instance'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}async function renderToString(element){let renderStream=await ReactDOMServerPages_default().renderToReadableStream(element);return await renderStream.allReady,streamToString(renderStream)}tryGetPreviewData=__webpack_require__(/*! ./api-utils/node/try-get-preview-data */"./dist/esm/server/api-utils/node/try-get-preview-data.js").tryGetPreviewData,warn=__webpack_require__(/*! ../build/output/log */"./dist/esm/build/output/log.js").warn,postProcessHTML=__webpack_require__(/*! ./post-process */"./dist/esm/server/post-process.js").postProcessHTML;class ServerRouter{constructor(pathname,query,as,{isFallback},isReady,basePath,locale,locales,defaultLocale,domainLocales,isPreview,isLocaleDomain){this.route=pathname.replace(/\/$/,"")||"/",this.pathname=pathname,this.query=query,this.asPath=as,this.isFallback=isFallback,this.basePath=basePath,this.locale=locale,this.locales=locales,this.defaultLocale=defaultLocale,this.isReady=isReady,this.domainLocales=domainLocales,this.isPreview=!!isPreview,this.isLocaleDomain=!!isLocaleDomain}push(){noRouter()}replace(){noRouter()}reload(){noRouter()}back(){noRouter()}forward(){noRouter()}prefetch(){noRouter()}beforePopState(){noRouter()}}function renderPageTree(App,Component,props){return/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsx)(App,{Component:Component,...props})}let invalidKeysMsg=(methodName,invalidKeys)=>{let docsPathname=`invalid-${methodName.toLocaleLowerCase()}-value`;return`Additional keys were returned from \`${methodName}\`. Properties intended for your component must be nested under the \`props\` key, e.g.:

	return { props: { title: 'My Title', content: '...' } }

Keys that need to be moved: ${invalidKeys.join(", ")}.
Read more: https://nextjs.org/docs/messages/${docsPathname}`};function checkRedirectValues(redirect,req,method){let{destination,permanent,statusCode,basePath}=redirect,errors=[],hasStatusCode=void 0!==statusCode,hasPermanent=void 0!==permanent;hasPermanent&&hasStatusCode?errors.push("`permanent` and `statusCode` can not both be provided"):hasPermanent&&"boolean"!=typeof permanent?errors.push("`permanent` must be `true` or `false`"):hasStatusCode&&!allowedStatusCodes.has(statusCode)&&errors.push(`\`statusCode\` must undefined or one of ${[...allowedStatusCodes].join(", ")}`);let destinationType=typeof destination;"string"!==destinationType&&errors.push(`\`destination\` should be string but received ${destinationType}`);let basePathType=typeof basePath;if("undefined"!==basePathType&&"boolean"!==basePathType&&errors.push(`\`basePath\` should be undefined or a false, received ${basePathType}`),errors.length>0)throw Object.defineProperty(Error(`Invalid redirect object returned from ${method} for ${req.url}
`+errors.join(" and ")+"\nSee more info here: https://nextjs.org/docs/messages/invalid-redirect-gssp"),"__NEXT_ERROR_CODE",{value:"E185",enumerable:!1,configurable:!0})}async function renderToHTMLImpl(req,res,pathname,query,renderOpts,extra,sharedContext,renderContext){var headers,err;let previewData,props,isPreview,source;(0,api_utils.setLazyProp)({req:req},"cookies",(headers=req.headers,function(){let{cookie}=headers;if(!cookie)return{};let{parse:parseCookieFn}=__webpack_require__(/*! next/dist/compiled/cookie */"./dist/compiled/cookie/index.js");return parseCookieFn(Array.isArray(cookie)?cookie.join("; "):cookie)}));let metadata={};if(metadata.assetQueryString=renderOpts.dev&&renderOpts.assetQueryString||"",renderOpts.dev&&!metadata.assetQueryString){let userAgent=(req.headers["user-agent"]||"").toLowerCase();userAgent.includes("safari")&&!userAgent.includes("chrome")&&(metadata.assetQueryString=`?ts=${Date.now()}`)}sharedContext.deploymentId&&(metadata.assetQueryString+=`${metadata.assetQueryString?"&":"?"}dpl=${sharedContext.deploymentId}`),query=Object.assign({},query);let{err:err1,dev=!1,ampPath="",pageConfig={},buildManifest,reactLoadableManifest,ErrorDebug,getStaticProps,getStaticPaths,getServerSideProps,isNextDataRequest,params,previewProps,basePath,images,runtime:globalRuntime,isExperimentalCompile,expireTime}=renderOpts,{App}=extra,assetQueryString=metadata.assetQueryString,Document=extra.Document,Component=renderOpts.Component,isFallback=renderContext.isFallback??!1,notFoundSrcPage=renderContext.developmentNotFoundSourcePage;!function(query){for(let name of INTERNAL_QUERY_NAMES)delete query[name]}(query);let isSSG=!!getStaticProps,isBuildTimeSSG=isSSG&&renderOpts.nextExport,defaultAppGetInitialProps=App.getInitialProps===App.origGetInitialProps,hasPageGetInitialProps=!!(null==Component?void 0:Component.getInitialProps),hasPageScripts=null==Component?void 0:Component.unstable_scriptLoader,pageIsDynamic=isDynamicRoute(pathname),defaultErrorGetInitialProps="/_error"===pathname&&Component.getInitialProps===Component.origGetInitialProps;renderOpts.nextExport&&hasPageGetInitialProps&&!defaultErrorGetInitialProps&&warn(`Detected getInitialProps on page '${pathname}' while running export. It's recommended to use getStaticProps which has a more correct behavior for static exporting.
Read more: https://nextjs.org/docs/messages/get-initial-props-export`);let isAutoExport=!hasPageGetInitialProps&&defaultAppGetInitialProps&&!isSSG&&!getServerSideProps;if(isAutoExport&&!dev&&isExperimentalCompile&&(res.setHeader("Cache-Control",function({revalidate,expire}){let swrHeader="number"==typeof revalidate&&void 0!==expire&&revalidate<expire?`, stale-while-revalidate=${expire-revalidate}`:"";return 0===revalidate?"private, no-cache, no-store, max-age=0, must-revalidate":"number"==typeof revalidate?`s-maxage=${revalidate}${swrHeader}`:`s-maxage=${constants.CACHE_ONE_YEAR}${swrHeader}`}({revalidate:!1,expire:expireTime})),isAutoExport=!1),hasPageGetInitialProps&&isSSG)throw Object.defineProperty(Error(constants.SSG_GET_INITIAL_PROPS_CONFLICT+` ${pathname}`),"__NEXT_ERROR_CODE",{value:"E262",enumerable:!1,configurable:!0});if(hasPageGetInitialProps&&getServerSideProps)throw Object.defineProperty(Error(constants.SERVER_PROPS_GET_INIT_PROPS_CONFLICT+` ${pathname}`),"__NEXT_ERROR_CODE",{value:"E262",enumerable:!1,configurable:!0});if(getServerSideProps&&isSSG)throw Object.defineProperty(Error(constants.SERVER_PROPS_SSG_CONFLICT+` ${pathname}`),"__NEXT_ERROR_CODE",{value:"E262",enumerable:!1,configurable:!0});if(getServerSideProps&&"export"===renderOpts.nextConfigOutput)throw Object.defineProperty(Error('getServerSideProps cannot be used with "output: export". See more info here: https://nextjs.org/docs/advanced-features/static-html-export'),"__NEXT_ERROR_CODE",{value:"E369",enumerable:!1,configurable:!0});if(getStaticPaths&&!pageIsDynamic)throw Object.defineProperty(Error(`getStaticPaths is only allowed for dynamic SSG pages and was found on '${pathname}'.
Read more: https://nextjs.org/docs/messages/non-dynamic-getstaticpaths-usage`),"__NEXT_ERROR_CODE",{value:"E187",enumerable:!1,configurable:!0});if(getStaticPaths&&!isSSG)throw Object.defineProperty(Error(`getStaticPaths was added without a getStaticProps in ${pathname}. Without getStaticProps, getStaticPaths does nothing`),"__NEXT_ERROR_CODE",{value:"E447",enumerable:!1,configurable:!0});if(isSSG&&pageIsDynamic&&!getStaticPaths)throw Object.defineProperty(Error(`getStaticPaths is required for dynamic SSG pages and is missing for '${pathname}'.
Read more: https://nextjs.org/docs/messages/invalid-getstaticpaths-value`),"__NEXT_ERROR_CODE",{value:"E255",enumerable:!1,configurable:!0});let asPath=renderOpts.resolvedAsPath||req.url;if(dev){let{isValidElementType}=__webpack_require__(/*! next/dist/compiled/react-is */"./dist/compiled/react-is/index.js");if(!isValidElementType(Component))throw Object.defineProperty(Error(`The default export is not a React Component in page: "${pathname}"`),"__NEXT_ERROR_CODE",{value:"E286",enumerable:!1,configurable:!0});if(!isValidElementType(App))throw Object.defineProperty(Error('The default export is not a React Component in page: "/_app"'),"__NEXT_ERROR_CODE",{value:"E464",enumerable:!1,configurable:!0});if(!isValidElementType(Document))throw Object.defineProperty(Error('The default export is not a React Component in page: "/_document"'),"__NEXT_ERROR_CODE",{value:"E511",enumerable:!1,configurable:!0});if((isAutoExport||isFallback)&&(query={...query.amp?{amp:query.amp}:{}},asPath=`${pathname}${req.url.endsWith("/")&&"/"!==pathname&&!pageIsDynamic?"/":""}`,req.url=pathname),"/404"===pathname&&(hasPageGetInitialProps||getServerSideProps))throw Object.defineProperty(Error(`\`pages/404\` ${constants.STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR}`),"__NEXT_ERROR_CODE",{value:"E134",enumerable:!1,configurable:!0});if(STATIC_STATUS_PAGES.includes(pathname)&&(hasPageGetInitialProps||getServerSideProps))throw Object.defineProperty(Error(`\`pages${pathname}\` ${constants.STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR}`),"__NEXT_ERROR_CODE",{value:"E125",enumerable:!1,configurable:!0});(null==renderOpts?void 0:renderOpts.setIsrStatus)&&renderOpts.setIsrStatus(asPath,!!isSSG||!!isAutoExport||null)}for(let methodName of["getStaticProps","getServerSideProps","getStaticPaths"])if(null==Component?void 0:Component[methodName])throw Object.defineProperty(Error(`page ${pathname} ${methodName} ${constants.GSSP_COMPONENT_MEMBER_ERROR}`),"__NEXT_ERROR_CODE",{value:"E417",enumerable:!1,configurable:!0});await loadable_shared_runtime.preloadAll(),(isSSG||getServerSideProps)&&!isFallback&&previewProps&&(isPreview=!1!==(previewData=tryGetPreviewData(req,res,previewProps,!!renderOpts.multiZoneDraftMode)));let router=new ServerRouter(pathname,query,asPath,{isFallback:isFallback},!!(getServerSideProps||hasPageGetInitialProps||!defaultAppGetInitialProps&&!isSSG||isExperimentalCompile),basePath,renderOpts.locale,renderOpts.locales,renderOpts.defaultLocale,renderOpts.domainLocales,isPreview,getRequestMeta(req,"isLocaleDomain")),appRouter={back(){router.back()},forward(){router.forward()},refresh(){router.reload()},hmrRefresh(){},push(href,param){let{scroll}=void 0===param?{}:param;router.push(href,void 0,{scroll})},replace(href,param){let{scroll}=void 0===param?{}:param;router.replace(href,void 0,{scroll})},prefetch(href){router.prefetch(href)}},scriptLoader={},jsxStyleRegistry=(0,external_styled_jsx_namespaceObject.createStyleRegistry)(),ampState={ampFirst:!0===pageConfig.amp,hasQuery:!!query.amp,hybrid:"hybrid"===pageConfig.amp},inAmpMode=function(param){let{ampFirst=!1,hybrid=!1,hasQuery=!1}=void 0===param?{}:param;return ampFirst||hybrid&&hasQuery}(ampState),head=function(inAmpMode){void 0===inAmpMode&&(inAmpMode=!1);let head=[/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsx)("meta",{charSet:"utf-8"},"charset")];return inAmpMode||head.push(/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),head}(inAmpMode),reactLoadableModules=[],initialScripts={};hasPageScripts&&(initialScripts.beforeInteractive=[].concat(hasPageScripts()).filter(script=>"beforeInteractive"===script.props.strategy).map(script=>script.props));let AppContainer=({children})=>/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsx)(AppRouterContext.Provider,{value:appRouter,children:/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsx)(SearchParamsContext.Provider,{value:router.isReady&&router.query?new URL(router.asPath,"http://n").searchParams:new URLSearchParams,children:/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsx)(PathnameContextProviderAdapter,{router:router,isAutoExport:isAutoExport,children:/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsx)(PathParamsContext.Provider,{value:function(router){if(!router.isReady||!router.query)return null;let pathParams={};for(let key of Object.keys(function(normalizedRoute,param){let{includeSuffix=!1,includePrefix=!1,excludeOptionalTrailingSlash=!1}={},{parameterizedRoute,groups}=function(route,includeSuffix,includePrefix){let groups={},groupIndex=1,segments=[];for(let segment of removeTrailingSlash(route).slice(1).split("/")){let markerMatch=INTERCEPTION_ROUTE_MARKERS.find(m=>segment.startsWith(m)),paramMatches=segment.match(PARAMETER_PATTERN);if(markerMatch&&paramMatches&&paramMatches[2]){let{key,optional,repeat}=parseMatchedParameter(paramMatches[2]);groups[key]={pos:groupIndex++,repeat,optional},segments.push("/"+escapeStringRegexp(markerMatch)+"([^/]+?)")}else if(paramMatches&&paramMatches[2]){let{key,repeat,optional}=parseMatchedParameter(paramMatches[2]);groups[key]={pos:groupIndex++,repeat,optional},includePrefix&&paramMatches[1]&&segments.push("/"+escapeStringRegexp(paramMatches[1]));let s=repeat?optional?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";includePrefix&&paramMatches[1]&&(s=s.substring(1)),segments.push(s)}else segments.push("/"+escapeStringRegexp(segment));includeSuffix&&paramMatches&&paramMatches[3]&&segments.push(escapeStringRegexp(paramMatches[3]))}return{parameterizedRoute:segments.join(""),groups}}(normalizedRoute,includeSuffix,includePrefix),re=parameterizedRoute;return excludeOptionalTrailingSlash||(re+="(?:/)?"),{re:RegExp("^"+re+"$"),groups:groups}}(router.pathname).groups))pathParams[key]=router.query[key];return pathParams}(router),children:/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsx)(RouterContext.Provider,{value:router,children:/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsx)(AmpStateContext.Provider,{value:ampState,children:/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsx)(HeadManagerContext.Provider,{value:{updateHead:state=>{head=state},updateScripts:scripts=>{scriptLoader=scripts},scripts:initialScripts,mountedInstances:new Set},children:/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsx)(LoadableContext.Provider,{value:moduleName=>reactLoadableModules.push(moduleName),children:/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsx)(external_styled_jsx_namespaceObject.StyleRegistry,{registry:jsxStyleRegistry,children:/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsx)(ImageConfigContext.Provider,{value:images,children:children})})})})})})})})})}),Noop=()=>null,AppContainerWithIsomorphicFiberStructure=({children})=>/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsxs)(jsx_runtime_namespaceObject.Fragment,{children:[/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsx)(Noop,{}),/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsx)(AppContainer,{children:/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsxs)(jsx_runtime_namespaceObject.Fragment,{children:[dev?/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsxs)(jsx_runtime_namespaceObject.Fragment,{children:[children,/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsx)(Noop,{})]}):children,/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsx)(Noop,{})]})})]}),ctx={err:err1,req:isAutoExport?void 0:req,res:isAutoExport?void 0:res,pathname,query,asPath,locale:renderOpts.locale,locales:renderOpts.locales,defaultLocale:renderOpts.defaultLocale,AppTree:props=>/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsx)(AppContainerWithIsomorphicFiberStructure,{children:renderPageTree(App,Component,{...props,router})}),defaultGetInitialProps:async(docCtx,options={})=>{let{html,head:renderPageHead}=await docCtx.renderPage({enhanceApp:AppComp=>props=>/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsx)(AppComp,{...props})}),styles=jsxStyleRegistry.styles({nonce:options.nonce});return jsxStyleRegistry.flush(),{html,head:renderPageHead,styles}}},nextExport=!isSSG&&(renderOpts.nextExport||dev&&(isAutoExport||isFallback)),styledJsxInsertedHTML=()=>{let styles=jsxStyleRegistry.styles();return jsxStyleRegistry.flush(),/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsx)(jsx_runtime_namespaceObject.Fragment,{children:styles})};if(props=await loadGetInitialProps(App,{AppTree:ctx.AppTree,Component,router,ctx}),(isSSG||getServerSideProps)&&isPreview&&(props.__N_PREVIEW=!0),isSSG&&(props.__N_SSG=!0),isSSG&&!isFallback){let data,revalidate;try{data=await (0,tracer_.getTracer)().trace(trace_constants.RenderSpan.getStaticProps,{spanName:`getStaticProps ${pathname}`,attributes:{"next.route":pathname}},()=>getStaticProps({...pageIsDynamic?{params}:void 0,...isPreview?{draftMode:!0,preview:!0,previewData:previewData}:void 0,locales:[...renderOpts.locales??[]],locale:renderOpts.locale,defaultLocale:renderOpts.defaultLocale,revalidateReason:renderOpts.isOnDemandRevalidate?"on-demand":isBuildTimeSSG?"build":"stale"}))}catch(staticPropsError){throw staticPropsError&&"ENOENT"===staticPropsError.code&&delete staticPropsError.code,staticPropsError}if(null==data)throw Object.defineProperty(Error(constants.GSP_NO_RETURNED_VALUE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});let invalidKeys=Object.keys(data).filter(key=>"revalidate"!==key&&"props"!==key&&"redirect"!==key&&"notFound"!==key);if(invalidKeys.includes("unstable_revalidate"))throw Object.defineProperty(Error(constants.UNSTABLE_REVALIDATE_RENAME_ERROR),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});if(invalidKeys.length)throw Object.defineProperty(Error(invalidKeysMsg("getStaticProps",invalidKeys)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});if(void 0!==data.notFound&&void 0!==data.redirect)throw Object.defineProperty(Error(`\`redirect\` and \`notFound\` can not both be returned from ${isSSG?"getStaticProps":"getServerSideProps"} at the same time. Page: ${pathname}
See more info here: https://nextjs.org/docs/messages/gssp-mixed-not-found-redirect`),"__NEXT_ERROR_CODE",{value:"E454",enumerable:!1,configurable:!0});if("notFound"in data&&data.notFound){if("/404"===pathname)throw Object.defineProperty(Error('The /404 page can not return notFound in "getStaticProps", please remove it to continue!'),"__NEXT_ERROR_CODE",{value:"E121",enumerable:!1,configurable:!0});metadata.isNotFound=!0}if("redirect"in data&&data.redirect&&"object"==typeof data.redirect){if(checkRedirectValues(data.redirect,req,"getStaticProps"),isBuildTimeSSG)throw Object.defineProperty(Error(`\`redirect\` can not be returned from getStaticProps during prerendering (${req.url})
See more info here: https://nextjs.org/docs/messages/gsp-redirect-during-prerender`),"__NEXT_ERROR_CODE",{value:"E497",enumerable:!1,configurable:!0});data.props={__N_REDIRECT:data.redirect.destination,__N_REDIRECT_STATUS:getRedirectStatus(data.redirect)},void 0!==data.redirect.basePath&&(data.props.__N_REDIRECT_BASE_PATH=data.redirect.basePath),metadata.isRedirect=!0}if((dev||isBuildTimeSSG)&&!metadata.isNotFound&&!isSerializableProps(pathname,"getStaticProps",data.props))throw Object.defineProperty(Error("invariant: getStaticProps did not return valid props. Please report this."),"__NEXT_ERROR_CODE",{value:"E129",enumerable:!1,configurable:!0});if("revalidate"in data){if(data.revalidate&&"export"===renderOpts.nextConfigOutput)throw Object.defineProperty(Error('ISR cannot be used with "output: export". See more info here: https://nextjs.org/docs/advanced-features/static-html-export'),"__NEXT_ERROR_CODE",{value:"E201",enumerable:!1,configurable:!0});if("number"==typeof data.revalidate){if(Number.isInteger(data.revalidate)){if(data.revalidate<=0)throw Object.defineProperty(Error(`A page's revalidate option can not be less than or equal to zero for ${req.url}. A revalidate option of zero means to revalidate after _every_ request, and implies stale data cannot be tolerated.

To never revalidate, you can set revalidate to \`false\` (only ran once at build-time).
To revalidate as soon as possible, you can set the value to \`1\`.`),"__NEXT_ERROR_CODE",{value:"E311",enumerable:!1,configurable:!0});data.revalidate>31536e3&&console.warn(`Warning: A page's revalidate option was set to more than a year for ${req.url}. This may have been done in error.
To only run getStaticProps at build-time and not revalidate at runtime, you can set \`revalidate\` to \`false\`!`),revalidate=data.revalidate}else throw Object.defineProperty(Error(`A page's revalidate option must be seconds expressed as a natural number for ${req.url}. Mixed numbers, such as '${data.revalidate}', cannot be used.
Try changing the value to '${Math.ceil(data.revalidate)}' or using \`Math.ceil()\` if you're computing the value.`),"__NEXT_ERROR_CODE",{value:"E438",enumerable:!1,configurable:!0})}else if(!0===data.revalidate)revalidate=1;else if(!1===data.revalidate||void 0===data.revalidate)revalidate=!1;else throw Object.defineProperty(Error(`A page's revalidate option must be seconds expressed as a natural number. Mixed numbers and strings cannot be used. Received '${JSON.stringify(data.revalidate)}' for ${req.url}`),"__NEXT_ERROR_CODE",{value:"E161",enumerable:!1,configurable:!0})}else revalidate=!1;if(props.pageProps=Object.assign({},props.pageProps,"props"in data?data.props:void 0),metadata.cacheControl={revalidate,expire:void 0},metadata.pageData=props,metadata.isNotFound)return new RenderResult(null,{metadata})}if(getServerSideProps&&(props.__N_SSP=!0),getServerSideProps&&!isFallback){let data;let canAccessRes=!0,resOrProxy=res,deferredContent=!1;resOrProxy=new Proxy(res,{get:function(obj,prop){if(!canAccessRes){let message=`You should not access 'res' after getServerSideProps resolves.
Read more: https://nextjs.org/docs/messages/gssp-no-mutating-res`;if(deferredContent)throw Object.defineProperty(Error(message),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});warn(message)}return reflect.ReflectAdapter.get(obj,prop,res)}});try{data=await (0,tracer_.getTracer)().trace(trace_constants.RenderSpan.getServerSideProps,{spanName:`getServerSideProps ${pathname}`,attributes:{"next.route":pathname}},async()=>getServerSideProps({req:req,res:resOrProxy,query,resolvedUrl:renderOpts.resolvedUrl,...pageIsDynamic?{params}:void 0,...!1!==previewData?{draftMode:!0,preview:!0,previewData:previewData}:void 0,locales:[...renderOpts.locales??[]],locale:renderOpts.locale,defaultLocale:renderOpts.defaultLocale})),canAccessRes=!1,metadata.cacheControl={revalidate:0,expire:void 0}}catch(serverSidePropsError){throw"object"==typeof serverSidePropsError&&null!==serverSidePropsError&&"name"in serverSidePropsError&&"message"in serverSidePropsError&&"ENOENT"===serverSidePropsError.code&&delete serverSidePropsError.code,serverSidePropsError}if(null==data)throw Object.defineProperty(Error(constants.GSSP_NO_RETURNED_VALUE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});data.props instanceof Promise&&(deferredContent=!0);let invalidKeys=Object.keys(data).filter(key=>"props"!==key&&"redirect"!==key&&"notFound"!==key);if(data.unstable_notFound)throw Object.defineProperty(Error(`unstable_notFound has been renamed to notFound, please update the field to continue. Page: ${pathname}`),"__NEXT_ERROR_CODE",{value:"E516",enumerable:!1,configurable:!0});if(data.unstable_redirect)throw Object.defineProperty(Error(`unstable_redirect has been renamed to redirect, please update the field to continue. Page: ${pathname}`),"__NEXT_ERROR_CODE",{value:"E284",enumerable:!1,configurable:!0});if(invalidKeys.length)throw Object.defineProperty(Error(invalidKeysMsg("getServerSideProps",invalidKeys)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});if("notFound"in data&&data.notFound){if("/404"===pathname)throw Object.defineProperty(Error('The /404 page can not return notFound in "getStaticProps", please remove it to continue!'),"__NEXT_ERROR_CODE",{value:"E121",enumerable:!1,configurable:!0});return metadata.isNotFound=!0,new RenderResult(null,{metadata})}if("redirect"in data&&"object"==typeof data.redirect&&(checkRedirectValues(data.redirect,req,"getServerSideProps"),data.props={__N_REDIRECT:data.redirect.destination,__N_REDIRECT_STATUS:getRedirectStatus(data.redirect)},void 0!==data.redirect.basePath&&(data.props.__N_REDIRECT_BASE_PATH=data.redirect.basePath),metadata.isRedirect=!0),deferredContent&&(data.props=await data.props),(dev||isBuildTimeSSG)&&!isSerializableProps(pathname,"getServerSideProps",data.props))throw Object.defineProperty(Error("invariant: getServerSideProps did not return valid props. Please report this."),"__NEXT_ERROR_CODE",{value:"E31",enumerable:!1,configurable:!0});props.pageProps=Object.assign({},props.pageProps,data.props),metadata.pageData=props}if(!isSSG&&!getServerSideProps&&Object.keys((null==props?void 0:props.pageProps)||{}).includes("url")&&console.warn(`The prop \`url\` is a reserved prop in Next.js for legacy reasons and will be overridden on page ${pathname}
See more info here: https://nextjs.org/docs/messages/reserved-page-prop`),isNextDataRequest&&!isSSG||metadata.isRedirect)return new RenderResult(JSON.stringify(props),{metadata});if(isFallback&&(props.pageProps={}),isResSent(res)&&!isSSG)return new RenderResult(null,{metadata});let filteredBuildManifest=buildManifest;if(isAutoExport&&pageIsDynamic){let _page;let page=(_page=(function(page){let normalized=/^\/index(\/|$)/.test(page)&&!isDynamicRoute(page)?"/index"+page:"/"===page?"/index":ensureLeadingSlash(page);{let{posix}=__webpack_require__(/*! path */"path"),resolvedPage=posix.normalize(normalized);if(resolvedPage!==normalized)throw new NormalizeError("Requested and resolved page mismatch: "+normalized+" "+resolvedPage)}return normalized})(pathname).replace(/\\/g,"/")).startsWith("/index/")&&!isDynamicRoute(_page)?_page.slice(6):"/index"!==_page?_page:"/";page in filteredBuildManifest.pages&&(filteredBuildManifest={...filteredBuildManifest,pages:{...filteredBuildManifest.pages,[page]:[...filteredBuildManifest.pages[page],...filteredBuildManifest.lowPriorityFiles.filter(f=>f.includes("_buildManifest"))]},lowPriorityFiles:filteredBuildManifest.lowPriorityFiles.filter(f=>!f.includes("_buildManifest"))})}let Body=({children})=>inAmpMode?children:/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsx)("div",{id:"__next",children:children}),renderDocument=async()=>{let documentInitialPropsRes,styles;async function loadDocumentInitialProps(renderShell){let renderPage=async(options={})=>{if(ctx.err&&ErrorDebug)return renderShell&&renderShell(App,Component),{html:await renderToString(/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsx)(Body,{children:/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsx)(ErrorDebug,{})})),head};if(dev&&(props.router||props.Component))throw Object.defineProperty(Error("'router' and 'Component' can not be returned in getInitialProps from _app.js https://nextjs.org/docs/messages/cant-override-next-props"),"__NEXT_ERROR_CODE",{value:"E230",enumerable:!1,configurable:!0});let{App:EnhancedApp,Component:EnhancedComponent}="function"==typeof options?{App:App,Component:options(Component)}:{App:options.enhanceApp?options.enhanceApp(App):App,Component:options.enhanceComponent?options.enhanceComponent(Component):Component},stream=await renderShell(EnhancedApp,EnhancedComponent);return await stream.allReady,{html:await streamToString(stream),head}},documentCtx={...ctx,renderPage},docProps=await loadGetInitialProps(Document,documentCtx);if(isResSent(res)&&!isSSG)return null;if(!docProps||"string"!=typeof docProps.html)throw Object.defineProperty(Error(`"${getDisplayName(Document)}.getInitialProps()" should resolve to an object with a "html" prop set with a valid html string`),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return{docProps,documentCtx}}Document.__NEXT_BUILTIN_DOCUMENT__;let renderContent=(_App,_Component)=>{let EnhancedApp=_App||App,EnhancedComponent=_Component||Component;return ctx.err&&ErrorDebug?/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsx)(Body,{children:/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsx)(ErrorDebug,{})}):/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsx)(Body,{children:/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsx)(AppContainerWithIsomorphicFiberStructure,{children:renderPageTree(EnhancedApp,EnhancedComponent,{...props,router})})})},renderShell=async(EnhancedApp,EnhancedComponent)=>{let content=renderContent(EnhancedApp,EnhancedComponent);return await function({ReactDOMServer,element,streamOptions}){return(0,tracer_.getTracer)().trace(trace_constants.AppRenderSpan.renderToReadableStream,async()=>ReactDOMServer.renderToReadableStream(element,streamOptions))}({ReactDOMServer:ReactDOMServerPages_default(),element:content})},hasDocumentGetInitialProps=!!Document.getInitialProps,[rawStyledJsxInsertedHTML,content]=await Promise.all([renderToString(styledJsxInsertedHTML()),(async()=>{if(hasDocumentGetInitialProps){if(null===(documentInitialPropsRes=await loadDocumentInitialProps(renderShell)))return null;let{docProps}=documentInitialPropsRes;return docProps.html}{documentInitialPropsRes={};let stream=await renderShell(App,Component);return await stream.allReady,streamToString(stream)}})()]);if(null===content)return null;let{docProps}=documentInitialPropsRes||{};return hasDocumentGetInitialProps?(styles=docProps.styles,head=docProps.head):(styles=jsxStyleRegistry.styles(),jsxStyleRegistry.flush()),{contentHTML:rawStyledJsxInsertedHTML+content,documentElement:htmlProps=>/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsx)(Document,{...htmlProps,...docProps}),head,headTags:[],styles}};(0,tracer_.getTracer)().setRootSpanAttribute("next.route",renderOpts.page);let documentResult=await (0,tracer_.getTracer)().trace(trace_constants.RenderSpan.renderDocument,{spanName:`render route (pages) ${renderOpts.page}`,attributes:{"next.route":renderOpts.page}},async()=>renderDocument());if(!documentResult)return new RenderResult(null,{metadata});let dynamicImportsIds=new Set,dynamicImports=new Set;for(let mod of reactLoadableModules){let manifestItem=reactLoadableManifest[mod];manifestItem&&(dynamicImportsIds.add(manifestItem.id),manifestItem.files.forEach(item=>{dynamicImports.add(item)}))}let hybridAmp=ampState.hybrid,docComponentsRendered={},{assetPrefix,defaultLocale,disableOptimizedLoading,domainLocales,locale,locales,runtimeConfig}=renderOpts,htmlProps={__NEXT_DATA__:{props,page:pathname,query,buildId:sharedContext.buildId,assetPrefix:""===assetPrefix?void 0:assetPrefix,runtimeConfig,nextExport:!0===nextExport||void 0,autoExport:!0===isAutoExport||void 0,isFallback,isExperimentalCompile,dynamicIds:0===dynamicImportsIds.size?void 0:Array.from(dynamicImportsIds),err:renderOpts.err?(err=renderOpts.err,dev?(source="server",source=err[symbolError]||"server",{name:err.name,source,message:strip_ansi_default()(err.message),stack:err.stack,digest:err.digest}):{name:"Internal Server Error.",message:"500 - Internal Server Error.",statusCode:500}):void 0,gsp:!!getStaticProps||void 0,gssp:!!getServerSideProps||void 0,customServer:sharedContext.customServer,gip:!!hasPageGetInitialProps||void 0,appGip:!defaultAppGetInitialProps||void 0,locale,locales,defaultLocale,domainLocales,isPreview:!0===isPreview||void 0,notFoundSrcPage:notFoundSrcPage&&dev?notFoundSrcPage:void 0},strictNextHead:renderOpts.strictNextHead,buildManifest:filteredBuildManifest,docComponentsRendered,dangerousAsPath:router.asPath,canonicalBase:!renderOpts.ampPath&&getRequestMeta(req,"didStripLocale")?`${renderOpts.canonicalBase||""}/${renderOpts.locale}`:renderOpts.canonicalBase,ampPath,inAmpMode,isDevelopment:!!dev,hybridAmp,dynamicImports:Array.from(dynamicImports),dynamicCssManifest:new Set(renderOpts.dynamicCssManifest||[]),assetPrefix,unstable_runtimeJS:void 0,unstable_JsPreload:pageConfig.unstable_JsPreload,assetQueryString,scriptLoader,locale,disableOptimizedLoading,head:documentResult.head,headTags:documentResult.headTags,styles:documentResult.styles,crossOrigin:renderOpts.crossOrigin,optimizeCss:renderOpts.optimizeCss,nextConfigOutput:renderOpts.nextConfigOutput,nextScriptWorkers:renderOpts.nextScriptWorkers,runtime:globalRuntime,largePageDataBytes:renderOpts.largePageDataBytes,nextFontManifest:renderOpts.nextFontManifest,experimentalClientTraceMetadata:renderOpts.experimental.clientTraceMetadata},document=/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsx)(AmpStateContext.Provider,{value:ampState,children:/*#__PURE__*/(0,jsx_runtime_namespaceObject.jsx)(HtmlContext.Provider,{value:htmlProps,children:documentResult.documentElement(htmlProps)})}),documentHTML=await (0,tracer_.getTracer)().trace(trace_constants.RenderSpan.renderToString,async()=>renderToString(document));{let nonRenderedComponents=[];for(let comp of["Main","Head","NextScript","Html"])docComponentsRendered[comp]||nonRenderedComponents.push(comp);if(nonRenderedComponents.length){let missingComponentList=nonRenderedComponents.map(e=>`<${e} />`).join(", "),plural=1!==nonRenderedComponents.length?"s":"";console.warn(`Your custom Document (pages/_document) did not render all the required subcomponent${plural}.
Missing component${plural}: ${missingComponentList}
Read how to fix here: https://nextjs.org/docs/messages/missing-document-component`)}}let[renderTargetPrefix,renderTargetSuffix]=documentHTML.split("<next-js-internal-body-render-target></next-js-internal-body-render-target>",2),prefix="";documentHTML.startsWith(DOCTYPE)||(prefix+=DOCTYPE),prefix+=renderTargetPrefix,inAmpMode&&(prefix+="\x3c!-- __NEXT_DATA__ --\x3e");let content=prefix+documentResult.contentHTML+renderTargetSuffix;return new RenderResult(await postProcessHTML(pathname,content,renderOpts,{inAmpMode,hybridAmp}),{metadata})}let renderToHTML=(req,res,pathname,query,renderOpts,sharedContext,renderContext)=>renderToHTMLImpl(req,res,pathname,query,renderOpts,renderOpts,sharedContext,renderContext),ServerInsertedHTMLContext=/*#__PURE__*/external_react_default().createContext(null);function useServerInsertedHTML(callback){let addInsertedServerHTMLCallback=(0,external_react_namespaceObject.useContext)(ServerInsertedHTMLContext);addInsertedServerHTMLCallback&&addInsertedServerHTMLCallback(callback)}class PagesRouteModule extends RouteModule{constructor(options){super(options),this.components=options.components}render(req,res,context){return renderToHTMLImpl(req,res,context.page,context.query,context.renderOpts,{App:this.components.App,Document:this.components.Document},context.sharedContext,context.renderContext)}}let vendored={contexts:entrypoints_namespaceObject},pages_module=PagesRouteModule})(),module.exports=__webpack_exports__})();
//# sourceMappingURL=pages-turbo.runtime.dev.js.map