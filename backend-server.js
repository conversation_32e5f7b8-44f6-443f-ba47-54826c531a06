const http = require('http');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

const PORT = 3000;

// In-memory database
let users = [];
let sessions = [];
let chatRooms = [];
let messages = [];

// Helper functions
function generateId() {
  return crypto.randomBytes(16).toString('hex');
}

function hashPassword(password) {
  return crypto.createHash('sha256').update(password).digest('hex');
}

function generateToken() {
  return crypto.randomBytes(32).toString('hex');
}

function findUserByToken(token) {
  const session = sessions.find(s => s.token === token);
  if (!session) return null;
  return users.find(u => u.id === session.userId);
}

// API Routes
function handleAPI(req, res, url, method) {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Content-Type', 'application/json');

  if (method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  // Parse request body
  let body = '';
  req.on('data', chunk => {
    body += chunk.toString();
  });

  req.on('end', () => {
    let data = {};
    try {
      data = body ? JSON.parse(body) : {};
    } catch (e) {
      data = {};
    }

    // Route handling
    if (url === '/api/auth/register' && method === 'POST') {
      handleRegister(res, data);
    } else if (url === '/api/auth/login' && method === 'POST') {
      handleLogin(res, data);
    } else if (url === '/api/user/profile' && method === 'GET') {
      handleGetProfile(res, req);
    } else if (url === '/api/chat/start' && method === 'POST') {
      handleStartChat(res, req, data);
    } else if (url === '/api/chat/messages' && method === 'GET') {
      handleGetMessages(res, req);
    } else if (url === '/api/chat/send' && method === 'POST') {
      handleSendMessage(res, req, data);
    } else if (url === '/api/stats' && method === 'GET') {
      handleGetStats(res);
    } else {
      res.writeHead(404);
      res.end(JSON.stringify({ error: 'API endpoint not found' }));
    }
  });
}

// API Handlers
function handleRegister(res, data) {
  const { username, email, password, avatar, mood } = data;

  if (!username || !email || !password) {
    res.writeHead(400);
    res.end(JSON.stringify({ error: 'Username, email, and password are required' }));
    return;
  }

  // Check if user exists
  if (users.find(u => u.username === username || u.email === email)) {
    res.writeHead(400);
    res.end(JSON.stringify({ error: 'User already exists' }));
    return;
  }

  // Create user
  const user = {
    id: generateId(),
    username,
    email,
    passwordHash: hashPassword(password),
    avatar: avatar || 'fox',
    mood: mood || 'happy',
    isOnline: true,
    createdAt: new Date().toISOString(),
    lastSeen: new Date().toISOString()
  };

  users.push(user);

  // Create session
  const token = generateToken();
  sessions.push({
    id: generateId(),
    userId: user.id,
    token,
    createdAt: new Date().toISOString()
  });

  const { passwordHash, ...userWithoutPassword } = user;

  res.writeHead(200);
  res.end(JSON.stringify({
    success: true,
    user: userWithoutPassword,
    token,
    message: 'Registration successful!'
  }));
}

function handleLogin(res, data) {
  const { username, password } = data;

  if (!username || !password) {
    res.writeHead(400);
    res.end(JSON.stringify({ error: 'Username and password are required' }));
    return;
  }

  // Find user
  const user = users.find(u => u.username === username || u.email === username);
  if (!user || user.passwordHash !== hashPassword(password)) {
    res.writeHead(401);
    res.end(JSON.stringify({ error: 'Invalid credentials' }));
    return;
  }

  // Create session
  const token = generateToken();
  sessions.push({
    id: generateId(),
    userId: user.id,
    token,
    createdAt: new Date().toISOString()
  });

  // Update user status
  user.isOnline = true;
  user.lastSeen = new Date().toISOString();

  const { passwordHash, ...userWithoutPassword } = user;

  res.writeHead(200);
  res.end(JSON.stringify({
    success: true,
    user: userWithoutPassword,
    token,
    message: 'Login successful!'
  }));
}

function handleGetProfile(res, req) {
  const token = req.headers.authorization?.replace('Bearer ', '');
  const user = findUserByToken(token);

  if (!user) {
    res.writeHead(401);
    res.end(JSON.stringify({ error: 'Unauthorized' }));
    return;
  }

  const { passwordHash, ...userWithoutPassword } = user;

  res.writeHead(200);
  res.end(JSON.stringify({
    success: true,
    user: userWithoutPassword
  }));
}

function handleStartChat(res, req, data) {
  const token = req.headers.authorization?.replace('Bearer ', '');
  const user = findUserByToken(token);

  if (!user) {
    res.writeHead(401);
    res.end(JSON.stringify({ error: 'Unauthorized' }));
    return;
  }

  // Find another online user
  const otherUsers = users.filter(u => u.id !== user.id && u.isOnline);
  if (otherUsers.length === 0) {
    res.writeHead(200);
    res.end(JSON.stringify({
      success: false,
      message: 'No other users online right now. Try again later!'
    }));
    return;
  }

  const partner = otherUsers[Math.floor(Math.random() * otherUsers.length)];

  // Create chat room
  const chatRoom = {
    id: generateId(),
    user1Id: user.id,
    user2Id: partner.id,
    createdAt: new Date().toISOString(),
    status: 'active'
  };

  chatRooms.push(chatRoom);

  res.writeHead(200);
  res.end(JSON.stringify({
    success: true,
    chatRoom,
    partner: {
      id: partner.id,
      username: `Anonymous ${partner.avatar}`,
      avatar: partner.avatar,
      mood: partner.mood
    },
    message: 'Chat started successfully!'
  }));
}

function handleGetMessages(res, req) {
  const token = req.headers.authorization?.replace('Bearer ', '');
  const user = findUserByToken(token);

  if (!user) {
    res.writeHead(401);
    res.end(JSON.stringify({ error: 'Unauthorized' }));
    return;
  }

  const url = new URL(req.url, `http://localhost:${PORT}`);
  const chatRoomId = url.searchParams.get('chatRoomId');

  if (!chatRoomId) {
    res.writeHead(400);
    res.end(JSON.stringify({ error: 'Chat room ID required' }));
    return;
  }

  const roomMessages = messages.filter(m => m.chatRoomId === chatRoomId);

  res.writeHead(200);
  res.end(JSON.stringify({
    success: true,
    messages: roomMessages
  }));
}

function handleSendMessage(res, req, data) {
  const token = req.headers.authorization?.replace('Bearer ', '');
  const user = findUserByToken(token);

  if (!user) {
    res.writeHead(401);
    res.end(JSON.stringify({ error: 'Unauthorized' }));
    return;
  }

  const { chatRoomId, message, type, audioData } = data;

  if (!chatRoomId || !message) {
    res.writeHead(400);
    res.end(JSON.stringify({ error: 'Chat room ID and message are required' }));
    return;
  }

  const newMessage = {
    id: generateId(),
    chatRoomId,
    userId: user.id,
    username: `Anonymous ${user.avatar}`,
    message,
    type: type || 'text',
    audioData: audioData || null,
    createdAt: new Date().toISOString()
  };

  messages.push(newMessage);

  res.writeHead(200);
  res.end(JSON.stringify({
    success: true,
    message: newMessage
  }));
}

function handleGetStats(res) {
  res.writeHead(200);
  res.end(JSON.stringify({
    success: true,
    stats: {
      totalUsers: users.length,
      onlineUsers: users.filter(u => u.isOnline).length,
      totalChats: chatRooms.length,
      totalMessages: messages.length
    }
  }));
}

// Main server
const server = http.createServer((req, res) => {
  const url = req.url;
  const method = req.method;

  console.log(`${method} ${url}`);

  // Handle API routes
  if (url.startsWith('/api/')) {
    handleAPI(req, res, url, method);
    return;
  }

  // Handle static files (same as before)
  const mimeTypes = {
    '.html': 'text/html',
    '.js': 'text/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml'
  };

  let filePath = '.' + url;
  if (filePath === './') {
    filePath = './index.html';
  }

  const extname = String(path.extname(filePath)).toLowerCase();
  const mimeType = mimeTypes[extname] || 'application/octet-stream';

  fs.readFile(filePath, (error, content) => {
    if (error) {
      if (error.code === 'ENOENT') {
        res.writeHead(404, { 'Content-Type': 'text/html' });
        res.end(`
          <html>
            <head><title>404 - File Not Found</title></head>
            <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
              <h1>🔍 File Not Found</h1>
              <p>The file <code>${url}</code> was not found.</p>
              <div style="margin: 20px;">
                <a href="/" style="display: inline-block; margin: 10px; padding: 10px 20px; background: #2563eb; color: white; text-decoration: none; border-radius: 5px;">📱 SoulConnect App</a>
              </div>
            </body>
          </html>
        `);
      } else {
        res.writeHead(500);
        res.end(`Server Error: ${error.code}`);
      }
    } else {
      res.writeHead(200, { 'Content-Type': mimeType });
      res.end(content, 'utf-8');
    }
  });
});

server.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 SoulConnect Backend Server running at:`);
  console.log(`   Local:   http://localhost:${PORT}`);
  console.log(`   Network: http://************:${PORT}`);
  console.log(`   API:     http://************:${PORT}/api/`);
  console.log('');
  console.log('✅ Backend API ready!');
});
